{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEffect of aspirin on cardiovascular mortality in elderly patients <|end|>\n<|assistant|> \naspirin & cardiovascular & mortality & (elderly | geriatric) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMRI vs CT for detecting early stroke signs <|end|>\n<|assistant|> \n(MRI | \"magnetic resonance imaging\") & (CT | \"computed tomography\") & detect & (stroke | CVA) & early <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP levels in sepsis-induced ARDS <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & sepsis & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVentilator-associated pneumonia prevention with chlorhexidine <|end|>\n<|assistant|> \n(VAP | \"ventilator-associated pneumonia\") & prevention & (chlorhexidine | CHG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBeta-blockers in heart failure with preserved ejection fraction <|end|>\n<|assistant|> \n(beta-blockers | \"beta adrenergic blockers\") & (HFpEF | \"heart failure with preserved ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiagnostic accuracy of D-dimer for pulmonary embolism in pregnancy <|end|>\n<|assistant|> \n(D-dimer | \"fibrin degradation product\") & accuracy & (PE | \"pulmonary embolism\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContinuous glucose monitoring in type 1 diabetes management <|end|>\n<|assistant|> \n(CGM | \"continuous glucose monitoring\") & (T1D | \"type 1 diabetes\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNeonatal outcomes of antenatal corticosteroid administration <|end|>\n<|assistant|> \nneonatal & outcomes & (antenatal | prenatal) & corticosteroids <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRituximab for refractory myasthenia gravis <|end|>\n<|assistant|> \nrituximab & (refractory | resistant) & (MG | \"myasthenia gravis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound guidance for central venous catheter insertion <|end|>\n<|assistant|> \nultrasound & guidance & (CVC | \"central venous catheter\") & insertion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAssociation between vitamin D deficiency and multiple sclerosis progression <|end|>\n<|assistant|> \n(vitamin D | calciferol) & deficiency & (MS | \"multiple sclerosis\") & progression <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPost-op atrial fibrillation after cardiac surgery <|end|>\n<|assistant|> \n(post-op | postoperative) & (AF | \"atrial fibrillation\") & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of rapid strep test in pediatric pharyngitis <|end|>\n<|assistant|> \nsensitivity & (\"rapid strep test\" | RADT) & (pediatric | children) & pharyngitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin elevation in renal failure without ACS <|end|>\n<|assistant|> \ntroponin & elevation & (\"renal failure\" | CKD) & !(ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBisphosphonate-related osteonecrosis of the jaw risk factors <|end|>\n<|assistant|> \n(bisphosphonates | BPs) & (ONJ | \"osteonecrosis of the jaw\") & (\"risk factors\" | etiology) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin-guided antibiotic therapy in ICU patients <|end|>\n<|assistant|> \nprocalcitonin & guided & antibiotics & (ICU | \"intensive care unit\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscatheter aortic valve replacement vs surgical AVR in high-risk patients <|end|>\n<|assistant|> \n(TAVR | TAVI) & (SAVR | \"surgical aortic valve replacement\") & (\"high risk\" | elderly) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for treatment-resistant depression <|end|>\n<|assistant|> \nketamine & (\"treatment resistant depression\" | TRD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nColonoscopy surveillance intervals after polyp removal <|end|>\n<|assistant|> \ncolonoscopy & surveillance & intervals & (polypectomy | \"polyp removal\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMachine learning algorithms for detecting diabetic retinopathy <|end|>\n<|assistant|> \n\"machine learning\" & algorithms & detect & (DR | \"diabetic retinopathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nImpact of tight glucose control on surgical wound healing <|end|>\n<|assistant|> \n\"tight glucose control\" & (\"surgical wounds\" | \"wound healing\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFalse positive rates in PSA screening for prostate cancer <|end|>\n<|assistant|> \n\"false positive\" & (PSA | \"prostate specific antigen\") & screening & (\"prostate cancer\" | PCa) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHeparin-induced thrombocytopenia diagnosis with 4T score <|end|>\n<|assistant|> \n(HIT | \"heparin-induced thrombocytopenia\") & diagnosis & (\"4T score\" | \"four T score\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaccination hesitancy and measles outbreaks <|end|>\n<|assistant|> \n(vaccination | immunization) & hesitancy & (measles | rubeola) & outbreaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal microbiota transplantation for recurrent C. diff infection <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR range in mechanical heart valve patients <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & range & (\"mechanical heart valves\" | MHV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymph node metastasis in early stage oral cancer <|end|>\n<|assistant|> \n(metastasis | metastatic) & (lymph nodes | LNs) & (\"oral cancer\" | \"oral squamous cell carcinoma\") & \"early stage\" <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAnticoagulation in cerebral venous thrombosis <|end|>\n<|assistant|> \n(anticoagulation | AC) & (CVT | \"cerebral venous thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScreening protocols for Lynch syndrome <|end|>\n<|assistant|> \nscreening & protocols & (\"Lynch syndrome\" | HNPCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNon-invasive ventilation in COPD exacerbations <|end|>\n<|assistant|> \n(NIV | \"non-invasive ventilation\") & (COPD | \"chronic obstructive pulmonary disease\") & exacerbations <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid for trauma patients with hemorrhagic shock <|end|>\n<|assistant|> \n(TXA | tranexamic) & trauma & (\"hemorrhagic shock\" | bleeding) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBiomarkers for early detection of ovarian cancer <|end|>\n<|assistant|> \nbiomarkers & \"early detection\" & (\"ovarian cancer\" | EOC) & (CA-125 | HE4) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadiofrequency ablation vs surgery for small liver tumors <|end|>\n<|assistant|> \n(RFA | \"radiofrequency ablation\") & surgery & (\"liver tumors\" | HCC) & small <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPPI use and risk of C. diff infection <|end|>\n<|assistant|> \n(PPIs | \"proton pump inhibitors\") & risk & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nImmunotherapy response in NSCLC with PD-L1 expression <|end|>\n<|assistant|> \nimmunotherapy & response & (NSCLC | \"non-small cell lung cancer\") & (\"PD-L1\" | \"programmed death ligand 1\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEEG findings in non-convulsive status epilepticus <|end|>\n<|assistant|> \n(EEG | electroencephalography) & findings & (NCSE | \"non-convulsive status epilepticus\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing for initiation of renal replacement therapy <|end|>\n<|assistant|> \ntiming & (RRT | \"renal replacement therapy\") & initiation & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGenetic markers for warfarin dosing <|end|>\n<|assistant|> \n(genetic | pharmacogenetic) & markers & (warfarin | coumadin) & dosing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic ultrasound for pancreatic cyst diagnosis <|end|>\n<|assistant|> \n(EUS | \"endoscopic ultrasound\") & (\"pancreatic cysts\" | PCN) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion thresholds in chemotherapy patients <|end|>\n<|assistant|> \n\"platelet transfusion\" & thresholds & (chemotherapy | chemo) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLong-term outcomes of bariatric surgery in adolescents <|end|>\n<|assistant|> \n\"long term\" & outcomes & (\"bariatric surgery\" | gastric) & adolescents <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin as an acute phase reactant in sepsis <|end|>\n<|assistant|> \nferritin & (\"acute phase reactant\" | APR) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPET-CT staging accuracy in Hodgkin's lymphoma <|end|>\n<|assistant|> \n(PET-CT | \"positron emission tomography\") & staging & accuracy & (\"Hodgkin''s lymphoma\" | HL) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic prophylaxis in total joint arthroplasty <|end|>\n<|assistant|> \n(antibiotics | prophylaxis) & (TJA | \"total joint arthroplasty\" | \"joint replacement\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyroid storm diagnosis using Burch-Wartofsky criteria <|end|>\n<|assistant|> \n(thyroid storm | thyrotoxic) & diagnosis & (\"Burch-Wartofsky\" | BWS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk of progressive multifocal leukoencephalopathy with natalizumab <|end|>\n<|assistant|> \nrisk & (PML | \"progressive multifocal leukoencephalopathy\") & (natalizumab | Tysabri) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScreening for abdominal aortic aneurysm with ultrasound <|end|>\n<|assistant|> \nscreening & (AAA | \"abdominal aortic aneurysm\") & ultrasound <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressin vs norepinephrine in septic shock <|end|>\n<|assistant|> \n(vasopressin | ADH) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCervical cancer screening guidelines with HPV co-testing <|end|>\n<|assistant|> \n(screening | guidelines) & (\"cervical cancer\" | cervix) & (HPV | \"human papillomavirus\") & co-testing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDeep brain stimulation for Parkinson's disease tremor <|end|>\n<|assistant|> \n(DBS | \"deep brain stimulation\") & (\"Parkinson''s disease\" | PD) & tremor <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMortality benefit of therapeutic hypothermia after cardiac arrest <|end|>\n<|assistant|> \nmortality & benefit & (hypothermia | cooling) & (\"cardiac arrest\" | SCA) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptical coherence tomography in glaucoma progression <|end|>\n<|assistant|> \n(OCT | \"optical coherence tomography\") & (glaucoma | POAG) & progression <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal amphotericin B for mucormycosis treatment <|end|>\n<|assistant|> \n(\"liposomal amphotericin\" | AmBisome) & (mucormycosis | zygomycosis) & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictors of difficult airway in obese patients <|end|>\n<|assistant|> \npredictors & (\"difficult airway\" | intubation) & (obese | obesity) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal monitoring patterns in placental abruption <|end|>\n<|assistant|> \n(fetal | cardiotocography) & monitoring & (\"placental abruption\" | abruption) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStatins and new-onset diabetes risk <|end|>\n<|assistant|> \n(statins | HMG-CoA) & (\"new onset diabetes\" | NOD) & risk <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTEE-guided cardioversion for atrial fibrillation <|end|>\n<|assistant|> \n(TEE | \"transesophageal echocardiography\") & guided & (cardioversion | DCCV) & (AF | \"atrial fibrillation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRRT vs intermittent hemodialysis in acute kidney injury <|end|>\n<|assistant|> \n(CRRT | \"continuous renal replacement\") & (IHD | \"intermittent hemodialysis\") & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBRCA mutation carriers and contralateral breast cancer risk <|end|>\n<|assistant|> \n(BRCA | \"breast cancer gene\") & carriers & (contralateral | bilateral) & (\"breast cancer\" | BC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiagnostic yield of capsule endoscopy for obscure GI bleeding <|end|>\n<|assistant|> \n(yield | accuracy) & (\"capsule endoscopy\" | VCE) & (\"obscure GI bleeding\" | OGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin to guide antibiotic duration in pneumonia <|end|>\n<|assistant|> \nprocalcitonin & guide & antibiotics & duration & (pneumonia | CAP) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan in heart failure with reduced ejection fraction <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (HFrEF | \"heart failure with reduced ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP setting in ARDS management <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical site infection rates with mechanical bowel prep <|end|>\n<|assistant|> \n(SSI | \"surgical site infection\") & rates & (\"mechanical bowel preparation\" | MBP) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin I vs Troponin T in chronic kidney disease <|end|>\n<|assistant|> \n(troponin I | TnI) & (troponin T | TnT) & (CKD | \"chronic kidney disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFirst-line immunotherapy for metastatic melanoma <|end|>\n<|assistant|> \n(first-line | 1L) & immunotherapy & (metastatic | stage IV) & melanoma <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntraoperative neuromonitoring in thyroid surgery <|end|>\n<|assistant|> \n(IONM | \"intraoperative neuromonitoring\") & (thyroid | parathyroid) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin K antagonist reversal with PCC <|end|>\n<|assistant|> \n(VKA | \"vitamin K antagonist\") & reversal & (PCC | \"prothrombin complex concentrate\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nYield of next-generation sequencing in undiagnosed genetic disorders <|end|>\n<|assistant|> \n(yield | diagnostic) & (NGS | \"next generation sequencing\") & (\"genetic disorders\" | syndromes) & undiagnosed <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound elastography for liver fibrosis staging <|end|>\n<|assistant|> \n(ultrasound | sonoelastography) & elastography & (\"liver fibrosis\" | cirrhosis) & staging <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nApixaban vs warfarin in antiphospholipid syndrome <|end|>\n<|assistant|> \n(apixaban | Eliquis) & (warfarin | coumadin) & (APS | \"antiphospholipid syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk stratification for SUDEP in epilepsy patients <|end|>\n<|assistant|> \n(risk | stratification) & (SUDEP | \"sudden unexpected death in epilepsy\") & (epilepsy | seizures) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum free light chains in multiple myeloma diagnosis <|end|>\n<|assistant|> \n(SFLC | \"serum free light chains\") & (\"multiple myeloma\" | MM) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransradial vs transfemoral access in cardiac catheterization <|end|>\n<|assistant|> \n(transradial | radial) & (transfemoral | femoral) & (\"cardiac catheterization\" | angiography) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of dual antiplatelet therapy after DES <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (DES | \"drug eluting stent\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for CNS anomalies detection <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CNS | \"central nervous system\") & anomalies <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProphylactic antibiotics in neutropenic fever <|end|>\n<|assistant|> \n(prophylactic | prophylaxis) & antibiotics & (neutropenic | neutropenia) & fever <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDexamethasone for prevention of post-op nausea and vomiting <|end|>\n<|assistant|> \n(dexamethasone | Decadron) & prevention & (PONV | \"postoperative nausea and vomiting\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMicrobiome changes in inflammatory bowel disease <|end|>\n<|assistant|> \n(microbiome | microbiota) & changes & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of FDG-PET for occult malignancy detection <|end|>\n<|assistant|> \nsensitivity & (FDG-PET | \"fluorodeoxyglucose pet\") & (occult | unknown) & malignancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCryoablation for Barrett's esophagus treatment <|end|>\n<|assistant|> \n(cryoablation | freezing) & (\"Barrett''s esophagus\" | BE) & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nB-type natriuretic peptide levels in heart failure diagnosis <|end|>\n<|assistant|> \n(BNP | \"B-type natriuretic peptide\") & levels & (HF | \"heart failure\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular hyaluronic acid for knee osteoarthritis <|end|>\n<|assistant|> \n(intra-articular | IA) & (hyaluronic | viscosupplementation) & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk of TLS with venetoclax in CLL <|end|>\n<|assistant|> \nrisk & (TLS | \"tumor lysis syndrome\") & (venetoclax | Venclexta) & (CLL | \"chronic lymphocytic leukemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPercutaneous drainage vs surgery for diverticular abscess <|end|>\n<|assistant|> \n(percutaneous | PCD) & drainage & surgery & (diverticular | diverticulitis) & abscess <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for left ventricular assist devices <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & range & (LVAD | \"left ventricular assist device\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nImmunohistochemistry markers for mesothelioma diagnosis <|end|>\n<|assistant|> \n(IHC | immunohistochemistry) & markers & (mesothelioma | pleural) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLidocaine patch for post-herpetic neuralgia <|end|>\n<|assistant|> \n(lidocaine | lignocaine) & patch & (PHN | \"post-herpetic neuralgia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPhenytoin vs levetiracetam for seizure prophylaxis in TBI <|end|>\n<|assistant|> \n(phenytoin | Dilantin) & (levetiracetam | Keppra) & (seizures | epilepsy) & prophylaxis & (TBI | \"traumatic brain injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScreening colonoscopy intervals for ulcerative colitis patients <|end|>\n<|assistant|> \nscreening & colonoscopy & intervals & (UC | \"ulcerative colitis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFractional flow reserve vs angiography for PCI guidance <|end|>\n<|assistant|> \n(FFR | \"fractional flow reserve\") & (angiography | coronary) & (PCI | \"percutaneous coronary intervention\") & guidance <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmyloid PET for Alzheimer's disease diagnosis <|end|>\n<|assistant|> \n(amyloid | beta-amyloid) & (PET | \"positron emission tomography\") & (AD | \"Alzheimer''s disease\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin vs vancomycin for initial C. diff treatment <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (vancomycin | Vancocin) & (\"C. diff\" | \"Clostridium difficile\") & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCervical length measurement for preterm birth prediction <|end|>\n<|assistant|> \n(cervical | cervix) & length & measurement & (preterm | premature) & birth & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDirect oral anticoagulants in cancer-associated thrombosis <|end|>\n<|assistant|> \n(DOACs | \"direct oral anticoagulants\") & (cancer | malignancy) & (thrombosis | VTE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in upper GI bleeding <|end|>\n<|assistant|> \ntiming & endoscopy & (\"upper GI bleed\" | UGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSGLT2 inhibitors for heart failure without diabetes <|end|>\n<|assistant|> \n(SGLT2 | \"sodium-glucose cotransporter-2\") & inhibitors & (HF | \"heart failure\") & !diabetes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntraoperative cell salvage in obstetrics <|end|>\n<|assistant|> \n(ICS | \"intraoperative cell salvage\") & (obstetrics | OB) & (hemorrhage | bleeding) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echocardiography for infective endocarditis diagnosis <|end|>\n<|assistant|> \n(TTE | \"transthoracic echocardiography\") & (IE | \"infective endocarditis\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCryoprecipitate transfusion in hypofibrinogenemia <|end|>\n<|assistant|> \n(cryoprecipitate | cryo) & transfusion & (hypofibrinogenemia | fibrinogen) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMolecular testing for EGFR mutations in lung adenocarcinoma <|end|>\n<|assistant|> \n(molecular | genetic) & testing & (EGFR | \"epidermal growth factor receptor\") & (\"lung cancer\" | NSCLC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPreoperative carbohydrate loading in elective surgery <|end|>\n<|assistant|> \n(preoperative | pre-op) & (carbohydrate | CHO) & loading & (elective | scheduled) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacral neuromodulation for fecal incontinence <|end|>\n<|assistant|> \n(neuromodulation | SNS) & (fecal | bowel) & incontinence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in endotracheal intubation <|end|>\n<|assistant|> \ncuff & pressure & (endotracheal | ET) & intubation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlasma exchange for thrombotic thrombocytopenic purpura <|end|>\n<|assistant|> \n(TPE | \"therapeutic plasma exchange\") & (TTP | \"thrombotic thrombocytopenic purpura\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCoronary calcium score for cardiovascular risk assessment <|end|>\n<|assistant|> \n(CAC | \"coronary artery calcium\") & score & (CVD | \"cardiovascular disease\") & risk <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical approaches for recurrent inguinal hernia repair <|end|>\n<|assistant|> \nsurgical & approaches & (recurrent | recurrence) & (\"inguinal hernia\" | groin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal HbA1c target in elderly diabetics <|end|>\n<|assistant|> \n(HbA1c | \"glycated hemoglobin\") & target & (elderly | geriatric) & (diabetes | DM) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBone mineral density changes with aromatase inhibitors <|end|>\n<|assistant|> \n(BMD | \"bone mineral density\") & changes & (AIs | \"aromatase inhibitors\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntracranial pressure monitoring in traumatic brain injury <|end|>\n<|assistant|> \n(ICP | \"intracranial pressure\") & monitoring & (TBI | \"traumatic brain injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTelemedicine for chronic disease management in rural areas <|end|>\n<|assistant|> \n(telemedicine | telehealth) & (chronic | long-term) & management & rural <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiagnostic criteria for systemic lupus erythematosus <|end|>\n<|assistant|> \n(diagnostic | classification) & criteria & (SLE | \"systemic lupus erythematosus\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal echocardiography for congenital heart defect screening <|end|>\n<|assistant|> \n(fetal | prenatal) & echocardiography & (CHD | \"congenital heart defects\") & screening <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in PCI patients <|end|>\n<|assistant|> \n(platelet | aggregation) & testing & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaccination strategies during measles outbreaks <|end|>\n<|assistant|> \n(vaccination | immunization) & strategies & (measles | rubeola) & outbreaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation protocol for mechanical ventilation <|end|>\n<|assistant|> \n(sedation | analgesedation) & protocol & (\"mechanical ventilation\" | MV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAccuracy of rapid influenza diagnostic tests <|end|>\n<|assistant|> \n(accuracy | sensitivity) & (RIDT | \"rapid influenza test\") & (influenza | flu) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTamsulosin for ureteral stone expulsion <|end|>\n<|assistant|> \n(tamsulosin | Flomax) & (ureteral | ureteric) & (stones | urolithiasis) & expulsion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk of PML with rituximab therapy <|end|>\n<|assistant|> \nrisk & (PML | \"progressive multifocal leukoencephalopathy\") & (rituximab | Rituxan) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContinuous EEG monitoring in subarachnoid hemorrhage <|end|>\n<|assistant|> \n(cEEG | \"continuous EEG\") & monitoring & (SAH | \"subarachnoid hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of anticoagulation for DVT <|end|>\n<|assistant|> \nduration & (anticoagulation | AC) & (DVT | \"deep vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for myocarditis diagnosis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & (myocarditis | inflammatory) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum lactate as predictor of sepsis mortality <|end|>\n<|assistant|> \n(lactate | lactic) & predictor & (sepsis | septic) & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injections for carpal tunnel syndrome <|end|>\n<|assistant|> \n(corticosteroids | steroids) & injections & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal blood pressure target in hemorrhagic stroke <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & target & (hemorrhagic | ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count as prognostic marker in COVID-19 <|end|>\n<|assistant|> \n(lymphocyte | lymphopenia) & (prognostic | marker) & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation for differentiated thyroid cancer <|end|>\n<|assistant|> \n(RAI | radioiodine) & ablation & (DTC | \"differentiated thyroid cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntravesical BCG for non-muscle invasive bladder cancer <|end|>\n<|assistant|> \n(intravesical | bladder) & (BCG | \"Bacillus Calmette-Guérin\") & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal P2Y12 inhibitor in STEMI patients <|end|>\n<|assistant|> \n(P2Y12 | ADP) & inhibitor & (STEMI | \"ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of fine needle aspiration for thyroid nodules <|end|>\n<|assistant|> \nsensitivity & (FNA | \"fine needle aspiration\") & (thyroid | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine for post-op pain control <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (post-op | postoperative) & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk of contrast-induced nephropathy with IV contrast <|end|>\n<|assistant|> \nrisk & (CIN | \"contrast-induced nephropathy\") & (\"IV contrast\" | \"iodinated contrast\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR range in pregnancy with mechanical valves <|end|>\n<|assistant|> \n(INR | \"international ratio\") & range & pregnancy & (\"mechanical valves\" | MHV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic mucosal resection for early gastric cancer <|end|>\n<|assistant|> \n(EMR | \"endoscopic mucosal resection\") & (early | T1) & (\"gastric cancer\" | stomach) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin elevation in pulmonary embolism <|end|>\n<|assistant|> \ntroponin & elevation & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of appendicitis severity <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & predictor & (appendicitis | appendix) & severity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal surveillance for Barrett's esophagus <|end|>\n<|assistant|> \nsurveillance & (\"Barrett''s esophagus\" | BE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for metastatic non-small cell lung cancer <|end|>\n<|assistant|> \n(pembrolizumab | Keytruda) & (metastatic | stage IV) & (NSCLC | \"non-small cell lung cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVentricular tachycardia ablation in structural heart disease <|end|>\n<|assistant|> \n(VT | \"ventricular tachycardia\") & ablation & (\"structural heart disease\" | cardiomyopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotic cessation in sepsis <|end|>\n<|assistant|> \nprocalcitonin & guide & antibiotics & cessation & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in infective endocarditis <|end|>\n<|assistant|> \ntiming & surgery & (IE | \"infective endocarditis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer for ruling out DVT in pregnancy <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & (\"rule out\" | exclusion) & (DVT | \"deep vein thrombosis\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum amyloid A in rheumatoid arthritis activity <|end|>\n<|assistant|> \n(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\") & activity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for cirrhosis with portal vein thrombosis <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (cirrhosis | liver) & (PVT | \"portal vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscranial Doppler for vasospasm detection in SAH <|end|>\n<|assistant|> \n(TCD | \"transcranial Doppler\") & (vasospasm | VSP) & detection & (SAH | \"subarachnoid hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nChest tube size for pneumothorax drainage <|end|>\n<|assistant|> \n(chest tube | thoracostomy) & size & (pneumothorax | PTX) & drainage <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for ulcerative colitis maintenance <|end|>\n<|assistant|> \n(adalimumab | Humira) & (UC | \"ulcerative colitis\") & (maintenance | remission) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal hemoglobin threshold for RBC transfusion <|end|>\n<|assistant|> \n(hemoglobin | Hb) & threshold & (\"RBC transfusion\" | \"blood transfusion\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical resection vs SBRT for early lung cancer <|end|>\n<|assistant|> \n(surgery | resection) & (SBRT | \"stereotactic body radiotherapy\") & (early | stage I) & (\"lung cancer\" | NSCLC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFlecainide for atrial fibrillation rhythm control <|end|>\n<|assistant|> \n(flecainide | Tambocor) & (AF | \"atrial fibrillation\") & (\"rhythm control\" | cardioversion) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid in total knee arthroplasty <|end|>\n<|assistant|> \n(TXA | tranexamic) & (TKA | \"total knee arthroplasty\") & (bleeding | blood) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for osteomyelitis <|end|>\n<|assistant|> \nduration & antibiotics & (osteomyelitis | bone) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac troponin in aortic stenosis patients <|end|>\n<|assistant|> \n(troponin | cTn) & (AS | \"aortic stenosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for Crohn's disease assessment <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD) & assessment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBispectral index monitoring during general anesthesia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (anesthesia | sedation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol for adrenal insufficiency diagnosis <|end|>\n<|assistant|> \n(cortisol | hydrocortisone) & serum & (\"adrenal insufficiency\" | Addison''s) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in laryngeal mask airway <|end|>\n<|assistant|> \ncuff & pressure & (LMA | \"laryngeal mask airway\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for recurrent Clostridium difficile infection <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (recurrent | relapse) & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in variceal bleeding <|end|>\n<|assistant|> \ntiming & endoscopy & (variceal | esophageal) & bleeding <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT angiography for pulmonary embolism <|end|>\n<|assistant|> \nsensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in neurogenic shock <|end|>\n<|assistant|> \n(vasopressor | pressor) & choice & (neurogenic | spinal) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for acute kidney injury prediction <|end|>\n<|assistant|> \n(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\") & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP setting in obese patients during surgery <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting & (obese | obesity) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan for heart failure with preserved EF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (HFpEF | \"heart failure with preserved ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion threshold in preeclampsia <|end|>\n<|assistant|> \n(platelet | PLT) & transfusion & threshold & (preeclampsia | PET) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for atrial fibrillation with mechanical valve <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (AF | \"atrial fibrillation\") & (\"mechanical valve\" | MVR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for congenital diaphragmatic hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical outcomes of robotic vs laparoscopic hysterectomy <|end|>\n<|assistant|> \noutcomes & (robotic | da Vinci) & (laparoscopic | keyhole) & hysterectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of response to biologics in RA <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & predictor & response & (biologics | TNF) & (RA | \"rheumatoid arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT after coronary stenting <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (stenting | PCI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin for bacterial vs viral differentiation <|end|>\n<|assistant|> \nprocalcitonin & (bacterial | infection) & (viral | virus) & differentiation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone for preterm birth prevention <|end|>\n<|assistant|> \n(vaginal | intravaginal) & progesterone & (preterm | premature) & birth & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation for alcohol withdrawal syndrome <|end|>\n<|assistant|> \nsedation & (AWS | \"alcohol withdrawal\") & (delirium | DT) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echo for pericardial effusion detection <|end|>\n<|assistant|> \n(TTE | \"transthoracic echocardiography\") & (\"pericardial effusion\" | tamponade) & detection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular steroids for knee osteoarthritis pain <|end|>\n<|assistant|> \n(intra-articular | IA) & steroids & (knee | OA | osteoarthritis) & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in acute cholecystitis <|end|>\n<|assistant|> \ntiming & surgery & (\"acute cholecystitis\" | gallbladder) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum ferritin as marker of macrophage activation <|end|>\n<|assistant|> \nferritin & marker & (\"macrophage activation\" | HLH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff size for blood pressure measurement <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal biometry accuracy for gestational age dating <|end|>\n<|assistant|> \n(fetal | ultrasound) & biometry & accuracy & (\"gestational age\" | GA) & dating <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for familial hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | proprotein) & inhibitors & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal dose of vitamin D for deficiency treatment <|end|>\n<|assistant|> \ndose & (vitamin D | cholecalciferol) & deficiency & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum sodium correction rate in hyponatremia <|end|>\n<|assistant|> \n(sodium | Na) & correction & rate & (hyponatremia | low) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscatheter closure for patent foramen ovale <|end|>\n<|assistant|> \n(transcatheter | percutaneous) & closure & (PFO | \"patent foramen ovale\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for cellulitis <|end|>\n<|assistant|> \nduration & antibiotics & (cellulitis | skin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for arrhythmogenic right ventricular dysplasia <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & (ARVC | \"arrhythmogenic right ventricular cardiomyopathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotics in COPD exacerbations <|end|>\n<|assistant|> \nprocalcitonin & guide & antibiotics & (COPD | \"chronic obstructive pulmonary disease\") & exacerbations <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for left atrial appendage closure <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (LAAC | \"left atrial appendage closure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin I vs T for myocardial infarction diagnosis <|end|>\n<|assistant|> \n(troponin I | TnI) & (troponin T | TnT) & (MI | \"myocardial infarction\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of PET-CT for lymphoma staging <|end|>\n<|assistant|> \nsensitivity & (PET-CT | \"positron emission tomography\") & (lymphoma | HL | NHL) & staging <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressin in vasodilatory shock <|end|>\n<|assistant|> \n(vasopressin | ADH) & (vasodilatory | septic) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrine microscopy for acute pyelonephritis diagnosis <|end|>\n<|assistant|> \nurine & microscopy & (pyelonephritis | UTI) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP for one-lung ventilation <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting & (\"one lung\" | OLV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacral nerve stimulation for urinary incontinence <|end|>\n<|assistant|> \n(SNS | \"sacral nerve stimulation\") & (urinary | bladder) & incontinence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet count as predictor of preeclampsia severity <|end|>\n<|assistant|> \n(platelet | thrombocyte) & count & predictor & (preeclampsia | PET) & severity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of surgery in acute appendicitis <|end|>\n<|assistant|> \ntiming & surgery & (\"acute appendicitis\" | appendix) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum free light chains in amyloidosis diagnosis <|end|>\n<|assistant|> \n(SFLC | \"serum free light chains\") & (amyloidosis | AL) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransesophageal echo for left atrial thrombus <|end|>\n<|assistant|> \n(TEE | \"transesophageal echocardiography\") & (\"left atrial\" | LA) & thrombus <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in tracheostomy tubes <|end|>\n<|assistant|> \ncuff & pressure & (tracheostomy | trach) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for first C. diff infection <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (first | initial) & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of MRI for prostate cancer detection <|end|>\n<|assistant|> \nsensitivity & (MRI | \"magnetic resonance\") & (\"prostate cancer\" | PCa) & detection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in anaphylactic shock <|end|>\n<|assistant|> \n(vasopressor | epinephrine) & choice & (anaphylactic | anaphylaxis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary albumin-creatinine ratio for CKD staging <|end|>\n<|assistant|> \n(ACR | \"albumin creatinine ratio\") & (CKD | \"chronic kidney disease\") & staging <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP in ARDS with COVID-19 <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\") & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacituzumab govitecan for triple-negative breast cancer <|end|>\n<|assistant|> \n(sacituzumab | Trodelvy) & (TNBC | \"triple negative breast cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in CABG patients <|end|>\n<|assistant|> \n(platelet | aggregation) & testing & (CABG | \"coronary artery bypass\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for cirrhotic patients with PVT <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (cirrhosis | liver) & (PVT | \"portal vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal echocardiography for tetralogy of Fallot <|end|>\n<|assistant|> \n(fetal | prenatal) & echocardiography & (\"tetralogy of Fallot\" | TOF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical resection vs radiotherapy for acoustic neuroma <|end|>\n<|assistant|> \n(surgery | resection) & (radiotherapy | SRS) & (\"acoustic neuroma\" | schwannoma) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of failure in appendicitis antibiotics <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & predictor & failure & antibiotics & appendicitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of anticoagulation for PE <|end|>\n<|assistant|> \nduration & anticoagulation & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol for Cushing's syndrome diagnosis <|end|>\n<|assistant|> \ncortisol & serum & (\"Cushing''s syndrome\" | hypercortisolism) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscranial Doppler for brain death confirmation <|end|>\n<|assistant|> \n(TCD | \"transcranial Doppler\") & (\"brain death\" | cerebral) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nChest tube management for pleural effusion <|end|>\n<|assistant|> \n(chest tube | thoracostomy) & management & (\"pleural effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for Crohn's fistula healing <|end|>\n<|assistant|> \n(adalimumab | Humira) & (\"Crohn''s disease\" | CD) & (fistula | perianal) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal hemoglobin for transfusion in GI bleed <|end|>\n<|assistant|> \n(hemoglobin | Hb) & threshold & transfusion & (\"GI bleed\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSBRT vs surgery for renal cell carcinoma <|end|>\n<|assistant|> \n(SBRT | \"stereotactic body radiotherapy\") & surgery & (RCC | \"renal cell carcinoma\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFlecainide for supraventricular tachycardia <|end|>\n<|assistant|> \n(flecainide | Tambocor) & (SVT | \"supraventricular tachycardia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranexamic acid in spine surgery <|end|>\n<|assistant|> \n(TXA | tranexamic) & (spine | spinal) & surgery & (bleeding | blood) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of antibiotics for UTI <|end|>\n<|assistant|> \nduration & antibiotics & (UTI | \"urinary tract infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac troponin in cardiac contusion <|end|>\n<|assistant|> \ntroponin & (\"cardiac contusion\" | trauma) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for obscure GI bleeding <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (\"obscure GI bleeding\" | OGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in ICU sedation <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (ICU | \"intensive care\") & sedation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum cortisol in septic shock <|end|>\n<|assistant|> \ncortisol & serum & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff pressure in pediatric intubation <|end|>\n<|assistant|> \ncuff & pressure & (pediatric | children) & intubation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for severe C. diff infection <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & severe & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of endoscopy in lower GI bleed <|end|>\n<|assistant|> \ntiming & endoscopy & (\"lower GI bleed\" | LGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT for renal colic <|end|>\n<|assistant|> \nsensitivity & (CT | \"computed tomography\") & (\"renal colic\" | stones) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice in cardiogenic shock <|end|>\n<|assistant|> \n(vasopressor | pressor) & choice & (cardiogenic | cardiac) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for contrast nephropathy <|end|>\n<|assistant|> \n(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (CIN | \"contrast induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal PEEP in morbid obesity <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting & (\"morbid obesity\" | bariatric) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril-valsartan for acute decompensated HF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in ITP <|end|>\n<|assistant|> \n(platelet | PLT) & transfusion & (ITP | \"immune thrombocytopenia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR for VTE prophylaxis <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (VTE | \"venous thromboembolism\") & prophylaxis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for spina bifida <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"spina bifida\" | myelomeningocele) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgical outcomes of open vs endovascular AAA repair <|end|>\n<|assistant|> \noutcomes & (open | surgical) & (endovascular | EVAR) & (AAA | \"abdominal aortic aneurysm\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of IBD flare <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & predictor & (IBD | \"inflammatory bowel disease\") & flare <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT in NSTEMI <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (NSTEMI | \"non ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum procalcitonin for meningitis etiology <|end|>\n<|assistant|> \nprocalcitonin & (meningitis | CNS) & (bacterial | viral) & etiology <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaginal estrogen for GSM symptoms <|end|>\n<|assistant|> \n(vaginal | topical) & estrogen & (GSM | \"genitourinary syndrome of menopause\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal sedation for ICU delirium <|end|>\n<|assistant|> \nsedation & (ICU | \"intensive care\") & (delirium | agitation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransthoracic echo for endocarditis <|end|>\n<|assistant|> \n(TTE | \"transthoracic echocardiography\") & (endocarditis | IE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular hyaluronan for hip OA <|end|>\n<|assistant|> \n(intra-articular | IA) & (hyaluronan | hyaluronic) & (hip | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal timing of cholecystectomy in gallstone pancreatitis <|end|>\n<|assistant|> \ntiming & cholecystectomy & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum ferritin in hemophagocytic syndrome <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal cuff size for obese patients <|end|>\n<|assistant|> \ncuff & size & (obese | obesity) & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal biometry for growth restriction <|end|>\n<|assistant|> \n(fetal | ultrasound) & biometry & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for statin intolerance <|end|>\n<|assistant|> \n(PCSK9 | proprotein) & inhibitors & (statin | HMG-CoA) & intolerance <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal vitamin D dose for osteoporosis <|end|>\n<|assistant|> \ndose & (vitamin D | cholecalciferol) & osteoporosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerum sodium in SIADH <|end|>\n<|assistant|> \n(sodium | Na) & (SIADH | \"syndrome of inappropriate ADH\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure <|end|>\n<|assistant|> \n(transcatheter | percutaneous) & closure & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal antibiotic duration for bacteremia <|end|>\n<|assistant|> \nduration & antibiotics & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for sarcoidosis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & (sarcoidosis | granulomatous) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance in sepsis resuscitation <|end|>\n<|assistant|> \n(lactate | lactic) & clearance & sepsis & (resuscitation | EGDT) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injections for plantar fasciitis <|end|>\n<|assistant|> \n(steroids | corticosteroids) & injections & (\"plantar fasciitis\" | heel) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP target in ischemic stroke <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & target & (ischemic | thrombotic) & stroke <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count in COVID-19 prognosis <|end|>\n<|assistant|> \n(lymphocyte | lymphopenia) & (COVID | SARS-CoV-2) & prognosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for toxic nodular goiter <|end|>\n<|assistant|> \n(RAI | radioiodine) & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder cancer recurrence <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (\"bladder cancer\" | TCC) & recurrence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal P2Y12 inhibitor in NSTEMI <|end|>\n<|assistant|> \n(P2Y12 | ADP) & inhibitor & (NSTEMI | \"non ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of FNA for parotid tumors <|end|>\n<|assistant|> \nsensitivity & (FNA | \"fine needle aspiration\") & (parotid | salivary) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in hernia repair <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (hernia | inguinal) & repair <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisk of CIN with contrast CT <|end|>\n<|assistant|> \nrisk & (CIN | \"contrast induced nephropathy\") & (\"contrast CT\" | \"iodinated contrast\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal INR in pregnancy <|end|>\n<|assistant|> \n(INR | \"international ratio\") & pregnancy & anticoagulation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEMR for early esophageal cancer <|end|>\n<|assistant|> \n(EMR | \"endoscopic mucosal resection\") & early & (esophageal | EAC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin in myocarditis <|end|>\n<|assistant|> \ntroponin & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP in diverticulitis <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & diverticulitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurveillance for Barrett's esophagus <|end|>\n<|assistant|> \nsurveillance & (\"Barrett''s esophagus\" | BE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for NSCLC <|end|>\n<|assistant|> \n(pembrolizumab | Keytruda) & (NSCLC | \"non-small cell lung cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVT ablation in cardiomyopathy <|end|>\n<|assistant|> \n(VT | \"ventricular tachycardia\") & ablation & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for antibiotic stopping <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgery timing in IE <|end|>\n<|assistant|> \nsurgery & timing & (IE | \"infective endocarditis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer for DVT exclusion <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & (DVT | \"deep vein thrombosis\") & (\"rule out\" | exclusion) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSAA in rheumatoid arthritis <|end|>\n<|assistant|> \n(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR target in PVT <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (PVT | \"portal vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTCD for vasospasm <|end|>\n<|assistant|> \n(TCD | \"transcranial Doppler\") & vasospasm <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nChest tube for hemothorax <|end|>\n<|assistant|> \n(chest tube | thoracostomy) & hemothorax <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for UC <|end|>\n<|assistant|> \n(adalimumab | Humira) & (UC | \"ulcerative colitis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHb threshold for transfusion <|end|>\n<|assistant|> \n(hemoglobin | Hb) & threshold & transfusion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSBRT for lung cancer <|end|>\n<|assistant|> \n(SBRT | \"stereotactic body radiotherapy\") & (\"lung cancer\" | NSCLC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFlecainide for AF <|end|>\n<|assistant|> \n(flecainide | Tambocor) & (AF | \"atrial fibrillation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTXA in hip replacement <|end|>\n<|assistant|> \n(TXA | tranexamic) & (THA | \"total hip arthroplasty\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic duration for cellulitis <|end|>\n<|assistant|> \nduration & antibiotics & cellulitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin in aortic dissection <|end|>\n<|assistant|> \ntroponin & (\"aortic dissection\" | dissection) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for CD <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (CD | \"Crohn''s disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in TIVA <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (TIVA | \"total intravenous anesthesia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCortisol for adrenal crisis <|end|>\n<|assistant|> \ncortisol & (\"adrenal crisis\" | insufficiency) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff pressure in LMA <|end|>\n<|assistant|> \ncuff & pressure & (LMA | \"laryngeal mask airway\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for C. diff <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (\"C. diff\" | \"Clostridium difficile\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopy timing in UGIB <|end|>\n<|assistant|> \nendoscopy & timing & (UGIB | \"upper GI bleed\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CTA for PE <|end|>\n<|assistant|> \nsensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor in neurogenic shock <|end|>\n<|assistant|> \nvasopressor & (neurogenic | spinal) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nuNGAL for AKI <|end|>\n<|assistant|> \n(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPEEP in obesity <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & obesity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEntresto for HF <|end|>\n<|assistant|> \n(Entresto | sacubitril/valsartan) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in PET <|end|>\n<|assistant|> \n(platelet | PLT) & transfusion & (PET | preeclampsia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR target in AF <|end|>\n<|assistant|> \n(INR | \"international ratio\") & target & (AF | \"atrial fibrillation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI for CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic colectomy <|end|>\n<|assistant|> \n(robotic | da Vinci) & laparoscopic & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP for biologic response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (biologics | TNF) & response <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT duration after stent <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & stent <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for viral vs bacterial <|end|>\n<|assistant|> \nprocalcitonin & (viral | bacterial) & differentiation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone for preterm birth <|end|>\n<|assistant|> \nprogesterone & (preterm | premature) & birth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation for AWS <|end|>\n<|assistant|> \nsedation & (AWS | \"alcohol withdrawal\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTTE for pericardial effusion <|end|>\n<|assistant|> \n(TTE | \"transthoracic echocardiography\") & (\"pericardial effusion\" | tamponade) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIA steroids for OA <|end|>\n<|assistant|> \n(intra-articular | IA) & steroids & (OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgery timing for cholecystitis <|end|>\n<|assistant|> \nsurgery & timing & cholecystitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin in HLH <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff size for BP <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBiometry for GA <|end|>\n<|assistant|> \n(fetal | ultrasound) & biometry & (GA | \"gestational age\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for FH <|end|>\n<|assistant|> \n(PCSK9 | proprotein) & inhibitors & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D for deficiency <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & deficiency <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSodium in hyponatremia <|end|>\n<|assistant|> \nsodium & hyponatremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nASD closure <|end|>\n<|assistant|> \n(ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for bacteremia <|end|>\n<|assistant|> \nantibiotics & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCMR for myocarditis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate in sepsis <|end|>\n<|assistant|> \nlactate & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injections for CTS <|end|>\n<|assistant|> \nsteroids & injections & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP target in ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & target & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte in COVID <|end|>\n<|assistant|> \nlymphocyte & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRAI for thyroid cancer <|end|>\n<|assistant|> \n(RAI | radioiodine) & (\"thyroid cancer\" | carcinoma) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for NMIBC <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nP2Y12 for STEMI <|end|>\n<|assistant|> \n(P2Y12 | ADP) & inhibitor & (STEMI | \"ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFNA for thyroid <|end|>\n<|assistant|> \n(FNA | \"fine needle aspiration\") & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine for pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCIN risk with contrast <|end|>\n<|assistant|> \nrisk & (CIN | \"contrast induced nephropathy\") & contrast <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR in pregnancy <|end|>\n<|assistant|> \n(INR | \"international ratio\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEMR for EGC <|end|>\n<|assistant|> \n(EMR | \"endoscopic mucosal resection\") & (EGC | \"early gastric cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin in PE <|end|>\n<|assistant|> \ntroponin & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP in appendicitis <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & appendicitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBarrett's surveillance <|end|>\n<|assistant|> \nsurveillance & (\"Barrett''s esophagus\" | BE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPembrolizumab for lung cancer <|end|>\n<|assistant|> \n(pembrolizumab | Keytruda) & (\"lung cancer\" | NSCLC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVT ablation <|end|>\n<|assistant|> \n(VT | \"ventricular tachycardia\") & ablation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin stop antibiotics <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgery for IE <|end|>\n<|assistant|> \nsurgery & (IE | \"infective endocarditis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer rule out DVT <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & (\"rule out\" | exclusion) & (DVT | \"deep vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSAA in RA <|end|>\n<|assistant|> \n(SAA | \"serum amyloid A\") & (RA | \"rheumatoid arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR for PVT <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (PVT | \"portal vein thrombosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTCD for SAH <|end|>\n<|assistant|> \n(TCD | \"transcranial Doppler\") & (SAH | \"subarachnoid hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nChest tube for pneumothorax <|end|>\n<|assistant|> \n(chest tube | thoracostomy) & pneumothorax <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdalimumab for colitis <|end|>\n<|assistant|> \n(adalimumab | Humira) & (colitis | UC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHb for transfusion <|end|>\n<|assistant|> \n(hemoglobin | Hb) & transfusion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSBRT for lung <|end|>\n<|assistant|> \n(SBRT | \"stereotactic body radiotherapy\") & lung <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFlecainide for SVT <|end|>\n<|assistant|> \n(flecainide | Tambocor) & (SVT | \"supraventricular tachycardia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTXA for TKA <|end|>\n<|assistant|> \n(TXA | tranexamic) & (TKA | \"total knee arthroplasty\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for UTI <|end|>\n<|assistant|> \nantibiotics & (UTI | \"urinary tract infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin in contusion <|end|>\n<|assistant|> \ntroponin & contusion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule for CD <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (CD | \"Crohn''s disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS for sedation <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & sedation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCortisol for crisis <|end|>\n<|assistant|> \ncortisol & crisis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff for LMA <|end|>\n<|assistant|> \ncuff & (LMA | \"laryngeal mask airway\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFidaxomicin for CDI <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopy for UGIB <|end|>\n<|assistant|> \nendoscopy & (UGIB | \"upper GI bleed\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCTA for PE <|end|>\n<|assistant|> \n(CTA | \"CT angiography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor for shock <|end|>\n<|assistant|> \nvasopressor & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nuNGAL for AKI <|end|>\n<|assistant|> \n(uNGAL | \"urinary neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPEEP for ARDS <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEntresto for HF <|end|>\n<|assistant|> \n(Entresto | sacubitril/valsartan) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet for PET <|end|>\n<|assistant|> \nplatelet & (PET | preeclampsia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR for AF <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (AF | \"atrial fibrillation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMRI for CDH <|end|>\n<|assistant|> \n(MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic for colectomy <|end|>\n<|assistant|> \nrobotic & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP for biologics <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & biologics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT after PCI <|end|>\n<|assistant|> \n(DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin for stopping <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone for PTB <|end|>\n<|assistant|> \nprogesterone & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation for withdrawal <|end|>\n<|assistant|> \nsedation & withdrawal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTTE for effusion <|end|>\n<|assistant|> \n(TTE | \"transthoracic echocardiography\") & effusion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroids for OA <|end|>\n<|assistant|> \nsteroids & (OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgery for cholecystitis <|end|>\n<|assistant|> \nsurgery & cholecystitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin for HLH <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff for BP <|end|>\n<|assistant|> \ncuff & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBiometry for IUGR <|end|>\n<|assistant|> \nbiometry & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | proprotein) & inhibitors & hypercholesterolemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D for bones <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (bones | osteoporosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSodium correction <|end|>\n<|assistant|> \nsodium & correction & hyponatremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nASD repair <|end|>\n<|assistant|> \n(ASD | \"atrial septal defect\") & repair <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for blood infection <|end|>\n<|assistant|> \nantibiotics & (bacteremia | \"blood infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMRI for heart inflammation <|end|>\n<|assistant|> \n(MRI | \"magnetic resonance\") & (myocarditis | inflammation) & heart <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate for sepsis <|end|>\n<|assistant|> \nlactate & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroids for carpal tunnel <|end|>\n<|assistant|> \nsteroids & (\"carpal tunnel\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP in brain bleed <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymph in COVID <|end|>\n<|assistant|> \nlymphocyte & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRAI for cancer <|end|>\n<|assistant|> \n(RAI | radioiodine) & (\"thyroid cancer\" | carcinoma) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & bladder <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nP2Y12 for heart attack <|end|>\n<|assistant|> \n(P2Y12 | ADP) & inhibitor & (MI | \"heart attack\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFNA for nodules <|end|>\n<|assistant|> \n(FNA | \"fine needle aspiration\") & nodules <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBupivacaine for surgery pain <|end|>\n<|assistant|> \n(bupivacaine | local) & surgery & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrast kidney risk <|end|>\n<|assistant|> \nrisk & (CIN | \"contrast nephropathy\") & contrast <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood thinner in pregnancy <|end|>\n<|assistant|> \n(anticoagulation | \"blood thinner\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScope for stomach cancer <|end|>\n<|assistant|> \nendoscopy & (\"stomach cancer\" | gastric) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTroponin in lung clot <|end|>\n<|assistant|> \ntroponin & (PE | \"lung clot\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP for appendix <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & appendicitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScope for Barrett's <|end|>\n<|assistant|> \nendoscopy & (\"Barrett''s esophagus\" | BE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKeytruda for lung <|end|>\n<|assistant|> \n(pembrolizumab | Keytruda) & lung <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAblation for VT <|end|>\n<|assistant|> \nablation & (VT | \"ventricular tachycardia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCT stop abx <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSurgery for endocarditis <|end|>\n<|assistant|> \nsurgery & endocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer for clot <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & (DVT | clot) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSAA for arthritis <|end|>\n<|assistant|> \n(SAA | \"serum amyloid A\") & arthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR for liver clot <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (PVT | \"liver clot\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTCD for brain bleed <|end|>\n<|assistant|> \n(TCD | \"transcranial Doppler\") & (SAH | \"brain bleed\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTube for collapsed lung <|end|>\n<|assistant|> \n(chest tube | thoracostomy) & (pneumothorax | \"collapsed lung\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHumira for UC <|end|>\n<|assistant|> \n(adalimumab | Humira) & (UC | \"ulcerative colitis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood level for transfusion <|end|>\n<|assistant|> \n(hemoglobin | Hb) & transfusion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadiation for lung tumor <|end|>\n<|assistant|> \n(SBRT | radiotherapy) & (\"lung tumor\" | cancer) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTambocor for fast heart <|end|>\n<|assistant|> \n(flecainide | Tambocor) & (tachycardia | \"fast heart\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTXA for knee replacement <|end|>\n<|assistant|> \n(TXA | tranexamic) & (\"knee replacement\" | TKA) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for bladder infection <|end|>\n<|assistant|> \nantibiotics & (UTI | \"bladder infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHeart enzyme in trauma <|end|>\n<|assistant|> \ntroponin & trauma <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPill cam for Crohn's <|end|>\n<|assistant|> \n(capsule | \"pill cam\") & endoscopy & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBrain monitor for anesthesia <|end|>\n<|assistant|> \n(BIS | monitor) & anesthesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid for adrenal problem <|end|>\n<|assistant|> \ncortisol & (\"adrenal problem\" | insufficiency) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff for airway <|end|>\n<|assistant|> \ncuff & (airway | LMA) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDificid for diarrhea <|end|>\n<|assistant|> \n(fidaxomicin | Dificid) & (diarrhea | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScope for vomit blood <|end|>\n<|assistant|> \nendoscopy & (hematemesis | \"vomit blood\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT scan for lung clot <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & (PE | \"lung clot\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPressor for low BP <|end|>\n<|assistant|> \n(vasopressor | pressor) & (\"low BP\" | hypotension) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrine test for kidney injury <|end|>\n<|assistant|> \n(urine | uNGAL) & (AKI | \"kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBreathing machine setting <|end|>\n<|assistant|> \n(PEEP | ventilator) & setting <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEntresto for weak heart <|end|>\n<|assistant|> \n(Entresto | sacubitril/valsartan) & (\"weak heart\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelets for preeclampsia <|end|>\n<|assistant|> \nplatelets & transfusion & preeclampsia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood thinner level <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (warfarin | \"blood thinner\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBaby MRI for hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | hernia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobot surgery for colon <|end|>\n<|assistant|> \nrobotic & surgery & (colon | colectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInflammation marker for biologics <|end|>\n<|assistant|> \n(CRP | ESR) & biologics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTwo blood thinners after stent <|end|>\n<|assistant|> \n(DAPT | \"dual antiplatelet\") & stent <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCT to stop antibiotics <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone to prevent early birth <|end|>\n<|assistant|> \nprogesterone & prevention & (\"early birth\" | preterm) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation for DT <|end|>\n<|assistant|> \nsedation & (DT | \"delirium tremens\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHeart echo for fluid <|end|>\n<|assistant|> \n(echo | echocardiography) & (effusion | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid shot for arthritis <|end|>\n<|assistant|> \n(steroid | corticosteroid) & injection & arthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOperation for gallbladder <|end|>\n<|assistant|> \nsurgery & (gallbladder | cholecystectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin for HLH <|end|>\n<|assistant|> \nferritin & (HLH | \"macrophage activation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP cuff size <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound for baby growth <|end|>\n<|assistant|> \n(ultrasound | sonogram) & (\"baby growth\" | IUGR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 for high cholesterol <|end|>\n<|assistant|> \n(PCSK9 | proprotein) & inhibitors & (\"high cholesterol\" | hyperlipidemia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D for bones <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (bones | osteoporosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSodium low correction <|end|>\n<|assistant|> \nsodium & correction & hyponatremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHole in heart repair <|end|>\n<|assistant|> \n(ASD | \"hole in heart\") & repair <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for blood infection <|end|>\n<|assistant|> \nantibiotics & (bacteremia | \"blood infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMRI for heart inflammation <|end|>\n<|assistant|> \n(MRI | \"magnetic resonance\") & (myocarditis | \"heart inflammation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate in infection <|end|>\n<|assistant|> \nlactate & (infection | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for wrist pain <|end|>\n<|assistant|> \nsteroid & injection & (\"wrist pain\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP control in brain bleed <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & control & (ICH | \"brain bleed\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWhite blood cell in COVID <|end|>\n<|assistant|> \n(lymphocyte | WBC) & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for thyroid <|end|>\n<|assistant|> \n(RAI | radioiodine) & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder cancer <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (\"bladder cancer\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood thinner after stent <|end|>\n<|assistant|> \n(P2Y12 | \"blood thinner\") & stent <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNeedle test for thyroid lump <|end|>\n<|assistant|> \n(FNA | \"needle test\") & (\"thyroid lump\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLong-lasting numbing for surgery <|end|>\n<|assistant|> \n(liposomal | long) & bupivacaine & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDye kidney damage <|end|>\n<|assistant|> \n(contrast | dye) & (nephropathy | \"kidney damage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWarfarin in pregnancy <|end|>\n<|assistant|> \n(warfarin | coumadin) & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCamera pill for Crohn's <|end|>\n<|assistant|> \n(capsule | \"camera pill\") & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBrain wave monitor <|end|>\n<|assistant|> \n(BIS | EEG) & monitor <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCortisol for Addison's <|end|>\n<|assistant|> \ncortisol & (Addison''s | \"adrenal insufficiency\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAirway cuff pressure <|end|>\n<|assistant|> \ncuff & pressure & airway <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiarrhea antibiotic <|end|>\n<|assistant|> \nantibiotics & (diarrhea | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nScope for stomach bleed <|end|>\n<|assistant|> \nendoscopy & (\"stomach bleed\" | UGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT for lung clot <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & (PE | \"lung clot\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPressure drug for shock <|end|>\n<|assistant|> \n(vasopressor | pressor) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrine marker for kidney injury <|end|>\n<|assistant|> \n(urine | biomarker) & (AKI | \"kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVentilator setting <|end|>\n<|assistant|> \n(PEEP | ventilator) & setting <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHeart failure pill <|end|>\n<|assistant|> \n(Entresto | sacubitril) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion in pregnancy sickness <|end|>\n<|assistant|> \nplatelet & transfusion & (preeclampsia | \"pregnancy sickness\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood test for warfarin <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (warfarin | coumadin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal scan for hernia <|end|>\n<|assistant|> \n(fetal | ultrasound) & scan & (CDH | hernia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobot colon surgery <|end|>\n<|assistant|> \nrobotic & (colon | colectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP for biologic drug <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & biologics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTwo blood thinners after heart stent <|end|>\n<|assistant|> \n(DAPT | \"dual antiplatelet\") & (stent | PCI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInfection marker stop antibiotics <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHormone prevent preterm birth <|end|>\n<|assistant|> \nprogesterone & prevention & (\"preterm birth\" | premature) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCalming DT <|end|>\n<|assistant|> \nsedation & (DT | \"delirium tremens\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHeart ultrasound fluid <|end|>\n<|assistant|> \n(echo | echocardiography) & (effusion | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKnee shot for arthritis <|end|>\n<|assistant|> \ninjection & (knee | OA) & arthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRemove gallbladder <|end|>\n<|assistant|> \nsurgery & (gallbladder | cholecystectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIron protein in HLH <|end|>\n<|assistant|> \nferritin & (HLH | \"macrophage activation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff too small BP <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBaby ultrasound size <|end|>\n<|assistant|> \n(fetal | ultrasound) & (size | growth) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNew drug high cholesterol <|end|>\n<|assistant|> \n(PCSK9 | evolocumab) & (\"high cholesterol\" | LDL) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D bone strength <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (bones | osteoporosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorrect low sodium <|end|>\n<|assistant|> \nsodium & correction & hyponatremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFix heart hole <|end|>\n<|assistant|> \nrepair & (ASD | \"heart hole\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics blood infection <|end|>\n<|assistant|> \nantibiotics & (bacteremia | \"blood infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMRI heart muscle <|end|>\n<|assistant|> \n(MRI | \"magnetic resonance\") & (myocarditis | \"heart muscle\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactic acid sepsis <|end|>\n<|assistant|> \n(lactate | \"lactic acid\") & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid shot wrist <|end|>\n<|assistant|> \nsteroid & injection & wrist <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP management hemorrhagic stroke <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & management & (hemorrhagic | ICH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymph count coronavirus <|end|>\n<|assistant|> \nlymphocyte & (COVID | coronavirus) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIodine treatment thyroid <|end|>\n<|assistant|> \n(RAI | radioiodine) & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBladder cancer immunotherapy <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (\"bladder cancer\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAnti-platelet after angioplasty <|end|>\n<|assistant|> \n(P2Y12 | \"anti-platelet\") & (stent | angioplasty) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNeedle biopsy thyroid <|end|>\n<|assistant|> \n(FNA | biopsy) & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nExtended release local anesthetic <|end|>\n<|assistant|> \n(liposomal | extended) & bupivacaine <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrast media nephrotoxicity <|end|>\n<|assistant|> \n(contrast | dye) & (nephropathy | nephrotoxicity) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood thinner pregnancy risk <|end|>\n<|assistant|> \n(warfarin | \"blood thinner\") & pregnancy & risk <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSwallowable camera Crohn's <|end|>\n<|assistant|> \n(capsule | \"swallowable camera\") & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAnesthesia depth monitor <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitor <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdrenal hormone replacement <|end|>\n<|assistant|> \ncortisol & (\"adrenal insufficiency\" | replacement) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTube pressure breathing <|end|>\n<|assistant|> \ncuff & pressure & (intubation | breathing) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nC. diff antibiotic <|end|>\n<|assistant|> \nantibiotics & (\"C. diff\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscope upper GI hemorrhage <|end|>\n<|assistant|> \nendoscopy & (\"upper GI bleed\" | UGIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nComputed tomography pulmonary embolism <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor choice hypotension <|end|>\n<|assistant|> \n(vasopressor | pressor) & choice & hypotension <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBiomarker acute kidney failure <|end|>\n<|assistant|> \n(uNGAL | biomarker) & (AKI | \"acute kidney failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVentilator PEEP setting <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & setting <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan heart function <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia pregnancy hypertension <|end|>\n<|assistant|> \n(platelet | thrombocytopenia) & (preeclampsia | \"pregnancy hypertension\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCoagulation test warfarin <|end|>\n<|assistant|> \n(INR | \"international ratio\") & (warfarin | coumadin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrenatal MRI diaphragmatic hernia <|end|>\n<|assistant|> \n(prenatal | fetal) & (MRI | \"magnetic resonance\") & (CDH | \"diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic-assisted colectomy <|end|>\n<|assistant|> \nrobotic & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP biologic response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & biologics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT duration coronary stent <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet\") & (stent | coronary) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antibiotic duration <|end|>\n<|assistant|> \nprocalcitonin & antibiotics & duration <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone preterm prevention <|end|>\n<|assistant|> \nprogesterone & prevention & preterm <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation alcohol withdrawal <|end|>\n<|assistant|> \nsedation & (\"alcohol withdrawal\" | AWS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEchocardiogram pericardial fluid <|end|>\n<|assistant|> \n(echo | echocardiography) & (\"pericardial fluid\" | effusion) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular injection osteoarthritis <|end|>\n<|assistant|> \n(intra-articular | IA) & injection & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCholecystectomy timing <|end|>\n<|assistant|> \ncholecystectomy & timing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin hemophagocytic syndrome <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure cuff size <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound fetal growth <|end|>\n<|assistant|> \nultrasound & fetal & growth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor hyperlipidemia <|end|>\n<|assistant|> \n(PCSK9 | evolocumab) & inhibitor & hyperlipidemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D osteoporosis <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & osteoporosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction rate <|end|>\n<|assistant|> \nhyponatremia & correction & rate <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtrial septal defect closure <|end|>\n<|assistant|> \n(ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics bacteremia <|end|>\n<|assistant|> \nantibiotics & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI myocarditis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate septic shock <|end|>\n<|assistant|> \nlactate & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injection carpal tunnel <|end|>\n<|assistant|> \nsteroid & injection & (\"carpal tunnel\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphopenia COVID-19 <|end|>\n<|assistant|> \n(lymphopenia | lymphocyte) & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation thyroid <|end|>\n<|assistant|> \n(RAI | radioiodine) & ablation & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG immunotherapy bladder <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & immunotherapy & bladder <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nP2Y12 inhibitor PCI <|end|>\n<|assistant|> \n(P2Y12 | ADP) & inhibitor & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFNA thyroid nodule <|end|>\n<|assistant|> \n(FNA | \"fine needle aspiration\") & (\"thyroid nodule\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine analgesia <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & analgesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrast-induced AKI <|end|>\n<|assistant|> \n(contrast | dye) & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWarfarin embryopathy <|end|>\n<|assistant|> \nwarfarin & (embryopathy | pregnancy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy IBD <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS depth anesthesia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & depth & anesthesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone adrenal insufficiency <|end|>\n<|assistant|> \n(hydrocortisone | cortisol) & (\"adrenal insufficiency\" | Addison''s) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndotracheal tube cuff <|end|>\n<|assistant|> \n(endotracheal | ET) & tube & cuff <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic-associated diarrhea <|end|>\n<|assistant|> \nantibiotics & (diarrhea | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEGD variceal bleeding <|end|>\n<|assistant|> \n(EGD | endoscopy) & (variceal | bleeding) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCTPA pulmonary embolism <|end|>\n<|assistant|> \n(CTPA | \"CT pulmonary angiogram\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNorepinephrine septic shock <|end|>\n<|assistant|> \n(norepinephrine | noradrenaline) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNGAL kidney injury <|end|>\n<|assistant|> \n(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMechanical ventilation PEEP <|end|>\n<|assistant|> \n(mechanical ventilation | MV) & (PEEP | \"positive end-expiratory pressure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nARNI heart failure <|end|>\n<|assistant|> \n(ARNI | sacubitril/valsartan) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHELLP syndrome platelets <|end|>\n<|assistant|> \n(HELLP | \"hemolysis elevated liver enzymes low platelets\") & (platelet | PLT) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR monitoring warfarin <|end|>\n<|assistant|> \n(INR | \"international ratio\") & monitoring & (warfarin | coumadin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal imaging congenital hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & imaging & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMinimally invasive colectomy <|end|>\n<|assistant|> \n(robotic | laparoscopic) & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInflammatory marker TNF inhibitor <|end|>\n<|assistant|> \n(CRP | ESR) & (TNF | biologics) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDual antiplatelet therapy duration <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCT-guided antibiotic therapy <|end|>\n<|assistant|> \n(PCT | procalcitonin) & guided & antibiotics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgestin preterm prevention <|end|>\n<|assistant|> \n(progesterone | progestin) & prevention & preterm <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBenzodiazepine alcohol withdrawal <|end|>\n<|assistant|> \n(benzodiazepine | BZD) & (\"alcohol withdrawal\" | AWS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPericardial effusion imaging <|end|>\n<|assistant|> \n(echo | imaging) & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nJoint injection osteoarthritis <|end|>\n<|assistant|> \njoint & injection & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAcute cholecystitis surgery <|end|>\n<|assistant|> \n(acute | surgery) & cholecystitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyperferritinemia HLH <|end|>\n<|assistant|> \n(hyperferritinemia | ferritin) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff hypertension measurement <|end|>\n<|assistant|> \ncuff & (hypertension | BP) & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSonogram fetal development <|end|>\n<|assistant|> \n(sonogram | ultrasound) & fetal & development <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 mAb hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | \"proprotein convertase subtilisin/kexin type 9\") & (mAb | inhibitor) & hypercholesterolemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCholecalciferol bone health <|end|>\n<|assistant|> \n(cholecalciferol | \"vitamin D\") & (\"bone health\" | osteoporosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia management <|end|>\n<|assistant|> \nhyponatremia & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nASD percutaneous closure <|end|>\n<|assistant|> \n(ASD | \"atrial septal defect\") & (percutaneous | closure) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic stewardship bacteremia <|end|>\n<|assistant|> \nantibiotics & stewardship & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac magnetic resonance inflammation <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & inflammation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactic acid septicemia <|end|>\n<|assistant|> \n(lactic | lactate) & septicemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injection neuropathy <|end|>\n<|assistant|> \n(corticosteroid | steroid) & injection & (neuropathy | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHypertension control hemorrhage <|end|>\n<|assistant|> \n(hypertension | BP) & control & (hemorrhage | ICH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte depletion coronavirus <|end|>\n<|assistant|> \n(lymphocyte | lymphopenia) & (COVID | coronavirus) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nI-131 thyroid ablation <|end|>\n<|assistant|> \n(I-131 | radioiodine) & ablation & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntravesical BCG carcinoma <|end|>\n<|assistant|> \n(intravesical | bladder) & (BCG | \"Bacillus Calmette-Guérin\") & (carcinoma | cancer) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClopidogrel stent thrombosis <|end|>\n<|assistant|> \n(clopidogrel | Plavix) & (stent | thrombosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCytology thyroid nodule <|end|>\n<|assistant|> \n(cytology | FNA) & (\"thyroid nodule\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSustained release bupivacaine <|end|>\n<|assistant|> \n(sustained | liposomal) & bupivacaine <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadiocontrast nephropathy <|end|>\n<|assistant|> \n(radiocontrast | dye) & nephropathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCoumarin pregnancy <|end|>\n<|assistant|> \n(coumarin | warfarin) & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWireless endoscopy IBD <|end|>\n<|assistant|> \n(wireless | capsule) & endoscopy & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEEG-derived depth monitor <|end|>\n<|assistant|> \n(EEG | \"bispectral index\") & monitor <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGlucocorticoid adrenal crisis <|end|>\n<|assistant|> \n(glucocorticoid | hydrocortisone) & (\"adrenal crisis\" | insufficiency) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTracheal tube cuff pressure <|end|>\n<|assistant|> \n(tracheal | endotracheal) & tube & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nC. difficile antimicrobial <|end|>\n<|assistant|> \nantimicrobials & (\"C. difficile\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTherapeutic endoscopy hemorrhage <|end|>\n<|assistant|> \n(therapeutic | endoscopy) & hemorrhage <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nComputed tomographic angiography PE <|end|>\n<|assistant|> \n(CTA | \"computed tomographic angiography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCatecholamine septic shock <|end|>\n<|assistant|> \n(catecholamine | norepinephrine) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRenal biomarker AKI <|end|>\n<|assistant|> \n(renal | uNGAL) & biomarker & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPositive end-expiratory pressure ARDS <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin receptor-neprilysin inhibitor HF <|end|>\n<|assistant|> \n(ARNI | sacubitril/valsartan) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia preeclampsia <|end|>\n<|assistant|> \n(thrombocytopenia | platelet) & preeclampsia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInternational normalized ratio anticoagulation <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & anticoagulation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrenatal diagnosis CDH <|end|>\n<|assistant|> \n(prenatal | fetal) & diagnosis & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobot-assisted colorectal surgery <|end|>\n<|assistant|> \n(robot-assisted | robotic) & (colorectal | colectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nC-reactive protein anti-TNF <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (\"anti-TNF\" | biologics) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT coronary intervention <|end|>\n<|assistant|> \n(DAPT | \"dual antiplatelet therapy\") & (\"coronary intervention\" | PCI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antimicrobial stewardship <|end|>\n<|assistant|> \nprocalcitonin & (antimicrobial | antibiotic) & stewardship <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone preterm birth prevention <|end|>\n<|assistant|> \nprogesterone & (\"preterm birth\" | PTB) & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedative-hypnotic alcohol withdrawal <|end|>\n<|assistant|> \n(sedative | benzodiazepine) & (\"alcohol withdrawal\" | AWS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEchocardiographic pericardial effusion <|end|>\n<|assistant|> \n(echocardiographic | echo) & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular corticosteroid OA <|end|>\n<|assistant|> \n(intra-articular | IA) & corticosteroid & (OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy timing <|end|>\n<|assistant|> \nlaparoscopic & cholecystectomy & timing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin hemophagocytic syndrome <|end|>\n<|assistant|> \nferritin & (\"hemophagocytic syndrome\" | HLH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSphygmomanometer cuff hypertension <|end|>\n<|assistant|> \n(sphygmomanometer | cuff) & hypertension <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nObstetric ultrasound growth <|end|>\n<|assistant|> \n(obstetric | fetal) & ultrasound & growth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibition LDL <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (LDL | cholesterol) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D3 bone density <|end|>\n<|assistant|> \n(vitamin D3 | cholecalciferol) & (\"bone density\" | osteoporosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction protocol <|end|>\n<|assistant|> \nhyponatremia & correction & protocol <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure <|end|>\n<|assistant|> \ntranscatheter & (ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntimicrobial therapy bacteremia <|end|>\n<|assistant|> \n(antimicrobial | antibiotic) & therapy & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCMR cardiac inflammation <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & inflammation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate sepsis mortality <|end|>\n<|assistant|> \nlactate & sepsis & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injection median neuropathy <|end|>\n<|assistant|> \nsteroid & injection & (\"median neuropathy\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure management ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & management & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphopenia SARS-CoV-2 <|end|>\n<|assistant|> \nlymphopenia & (SARS-CoV-2 | COVID) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioactive iodine thyroid <|end|>\n<|assistant|> \n(radioactive | I-131) & iodine & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG instillation bladder cancer <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & instillation & (\"bladder cancer\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nP2Y12 receptor antagonist stent <|end|>\n<|assistant|> \n(P2Y12 | antagonist) & stent <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFine-needle aspiration thyroid <|end|>\n<|assistant|> \n(FNA | \"fine-needle aspiration\") & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDepot bupivacaine formulation <|end|>\n<|assistant|> \n(depot | liposomal) & bupivacaine <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIodinated contrast nephropathy <|end|>\n<|assistant|> \n(iodinated | contrast) & nephropathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin K antagonist pregnancy <|end|>\n<|assistant|> \n(VKA | \"vitamin K antagonist\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule enteroscopy Crohn's <|end|>\n<|assistant|> \n(capsule | enteroscopy) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBispectral index sedation <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & sedation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid adrenal support <|end|>\n<|assistant|> \ncorticosteroid & (\"adrenal support\" | insufficiency) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndotracheal cuff pressure <|end|>\n<|assistant|> \n(endotracheal | ET) & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic C. difficile colitis <|end|>\n<|assistant|> \nantibiotics & (\"C. difficile\" | CDI) & colitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic hemostasis <|end|>\n<|assistant|> \nendoscopic & hemostasis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT angiogram pulmonary embolism <|end|>\n<|assistant|> \n(CTA | \"CT angiogram\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor sepsis <|end|>\n<|assistant|> \nvasopressor & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNGAL acute renal injury <|end|>\n<|assistant|> \n(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute renal injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMechanical ventilation settings <|end|>\n<|assistant|> \n(mechanical ventilation | MV) & settings <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan heart failure <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHELLP thrombocytopenia <|end|>\n<|assistant|> \n(HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR warfarin monitoring <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & (warfarin | coumadin) & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI diaphragmatic hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"diaphragmatic hernia\" | CDH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy outcomes <|end|>\n<|assistant|> \nrobotic & colectomy & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP TNF inhibitor response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (\"TNF inhibitor\" | biologics) & response <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT after PCI duration <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin antibiotic duration <|end|>\n<|assistant|> \nprocalcitonin & antibiotic & duration <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone prevent preterm <|end|>\n<|assistant|> \nprogesterone & prevent & preterm <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation AWS <|end|>\n<|assistant|> \nsedation & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEcho pericardial fluid <|end|>\n<|assistant|> \n(echo | echocardiography) & (\"pericardial fluid\" | effusion) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nJoint injection arthritis <|end|>\n<|assistant|> \njoint & injection & arthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCholecystectomy acute <|end|>\n<|assistant|> \ncholecystectomy & acute <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin HLH diagnosis <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP measurement cuff <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & measurement & cuff <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound fetal growth assessment <|end|>\n<|assistant|> \nultrasound & fetal & (\"growth assessment\" | IUGR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor cholesterol <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & cholesterol <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D bone mineral density <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (\"bone mineral density\" | BMD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia treatment <|end|>\n<|assistant|> \nhyponatremia & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nASD repair percutaneous <|end|>\n<|assistant|> \n(ASD | \"atrial septal defect\") & repair & percutaneous <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic selection bacteremia <|end|>\n<|assistant|> \nantibiotics & selection & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI inflammation <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & inflammation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate level sepsis <|end|>\n<|assistant|> \nlactate & level & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid shot carpal tunnel <|end|>\n<|assistant|> \nsteroid & injection & (\"carpal tunnel\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP control hemorrhagic stroke <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & control & (hemorrhagic | ICH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count COVID <|end|>\n<|assistant|> \nlymphocyte & count & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nI-131 ablation thyroid <|end|>\n<|assistant|> \n(I-131 | radioiodine) & ablation & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for bladder carcinoma <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (\"bladder carcinoma\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after stent <|end|>\n<|assistant|> \n(clopidogrel | Plavix) & stent <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFNA thyroid diagnosis <|end|>\n<|assistant|> \n(FNA | \"fine needle aspiration\") & thyroid & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nExtended-release local anesthetic <|end|>\n<|assistant|> \n(extended-release | liposomal) & bupivacaine <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrast-induced AKI <|end|>\n<|assistant|> \n(contrast | dye) & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWarfarin in gestation <|end|>\n<|assistant|> \nwarfarin & (gestation | pregnancy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy Crohn's disease <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS anesthesia depth <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & anesthesia & depth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone for Addison's <|end|>\n<|assistant|> \nhydrocortisone & (Addison''s | \"adrenal insufficiency\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nET tube cuff pressure <|end|>\n<|assistant|> \n(ET | \"endotracheal tube\") & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntimicrobial C. diff <|end|>\n<|assistant|> \nantimicrobials & (\"C. diff\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic treatment bleeding <|end|>\n<|assistant|> \nendoscopic & treatment & bleeding <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT PE diagnosis <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & (PE | \"pulmonary embolism\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor septic shock <|end|>\n<|assistant|> \nvasopressor & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary biomarker AKI <|end|>\n<|assistant|> \n(urinary | uNGAL) & biomarker & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPEEP ARDS management <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan HF <|end|>\n<|assistant|> \n(sacubitril valsartan | Entresto) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet count preeclampsia <|end|>\n<|assistant|> \n(platelet | PLT) & count & preeclampsia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR measurement <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal diagnosis diaphragmatic hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & diagnosis & (\"diaphragmatic hernia\" | CDH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic colorectal surgery <|end|>\n<|assistant|> \nrobotic & (colorectal | colectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP anti-TNF response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (\"anti-TNF\" | biologics) & response <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT duration after angioplasty <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & angioplasty <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin stop antibiotics <|end|>\n<|assistant|> \nprocalcitonin & stop & antibiotics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone prevent PTB <|end|>\n<|assistant|> \nprogesterone & prevent & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation alcohol withdrawal syndrome <|end|>\n<|assistant|> \nsedation & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEchocardiography pericardial effusion <|end|>\n<|assistant|> \n(echocardiography | echo) & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular steroid injection <|end|>\n<|assistant|> \n(intra-articular | IA) & steroid & injection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy acute <|end|>\n<|assistant|> \nlaparoscopic & cholecystectomy & acute <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin in HLH <|end|>\n<|assistant|> \nferritin & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure cuff accuracy <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & cuff & accuracy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal ultrasound IUGR <|end|>\n<|assistant|> \n(fetal | prenatal) & ultrasound & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitor therapy <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & therapy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D for osteoporosis <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & osteoporosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia correction guidelines <|end|>\n<|assistant|> \nhyponatremia & correction & guidelines <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPercutaneous ASD closure <|end|>\n<|assistant|> \npercutaneous & (ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic therapy bacteremia <|end|>\n<|assistant|> \nantibiotics & therapy & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCMR for cardiac sarcoid <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & (\"cardiac sarcoid\" | sarcoidosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate as sepsis marker <|end|>\n<|assistant|> \nlactate & sepsis & marker <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for CTS <|end|>\n<|assistant|> \nsteroid & injection & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure in ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphopenia in COVID-19 <|end|>\n<|assistant|> \nlymphopenia & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for Graves' disease <|end|>\n<|assistant|> \n(radioiodine | I-131) & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG for superficial bladder cancer <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & (\"superficial bladder cancer\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after PCI <|end|>\n<|assistant|> \n(clopidogrel | Plavix) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyroid FNA cytology <|end|>\n<|assistant|> \nthyroid & (FNA | \"fine needle aspiration\") & cytology <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine efficacy <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & efficacy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrast nephropathy prevention <|end|>\n<|assistant|> \n(contrast | dye) & nephropathy & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWarfarin during pregnancy <|end|>\n<|assistant|> \nwarfarin & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy in IBD <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring anesthesia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & anesthesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone adrenal insufficiency <|end|>\n<|assistant|> \nhydrocortisone & (\"adrenal insufficiency\" | Addison''s) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTracheal cuff pressure monitoring <|end|>\n<|assistant|> \n(tracheal | endotracheal) & cuff & pressure & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotics for C. diff colitis <|end|>\n<|assistant|> \nantibiotics & (\"C. diff\" | CDI) & colitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic hemostasis techniques <|end|>\n<|assistant|> \nendoscopic & hemostasis & techniques <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT diagnosis pulmonary embolism <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & diagnosis & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressors in septic shock <|end|>\n<|assistant|> \nvasopressors & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNGAL for acute kidney injury <|end|>\n<|assistant|> \n(NGAL | \"neutrophil gelatinase-associated lipocalin\") & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPEEP in ARDS management <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in heart failure <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet levels in HELLP <|end|>\n<|assistant|> \n(platelet | PLT) & levels & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nINR for anticoagulation <|end|>\n<|assistant|> \n(INR | \"international normalized ratio\") & anticoagulation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal imaging for CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & imaging & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic surgery for colon cancer <|end|>\n<|assistant|> \nrobotic & surgery & (\"colon cancer\" | colorectal) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP response to biologics <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & response & biologics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDAPT after coronary stenting <|end|>\n<|assistant|> \n(DAPT | \"dual antiplatelet therapy\") & (\"coronary stenting\" | stent) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin-guided antibiotic cessation <|end|>\n<|assistant|> \n(PCT | procalcitonin) & guided & antibiotics & cessation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone for preterm birth prevention <|end|>\n<|assistant|> \nvaginal & progesterone & (\"preterm birth\" | PTB) & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation in alcohol withdrawal <|end|>\n<|assistant|> \nsedation & (\"alcohol withdrawal\" | AWS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEcho for pericardial effusion <|end|>\n<|assistant|> \n(echo | echocardiography) & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injection for knee OA <|end|>\n<|assistant|> \nsteroid & injection & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLaparoscopic cholecystectomy in acute cholecystitis <|end|>\n<|assistant|> \nlaparoscopic & cholecystectomy & (\"acute cholecystitis\" | gallbladder) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin level in HLH <|end|>\n<|assistant|> \nferritin & level & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff size for blood pressure measurement <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound assessment of fetal growth <|end|>\n<|assistant|> \nultrasound & assessment & fetal & growth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors for hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & hypercholesterolemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D supplementation for osteoporosis <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & supplementation & osteoporosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHyponatremia management guidelines <|end|>\n<|assistant|> \nhyponatremia & management & guidelines <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPercutaneous closure of ASD <|end|>\n<|assistant|> \npercutaneous & closure & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic choice in bacteremia <|end|>\n<|assistant|> \nantibiotics & choice & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI for myocarditis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate as prognostic marker in sepsis <|end|>\n<|assistant|> \nlactate & prognostic & marker & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid injection for carpal tunnel syndrome <|end|>\n<|assistant|> \n(corticosteroid | steroid) & injection & (\"carpal tunnel syndrome\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlood pressure control in ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte count in COVID <|end|>\n<|assistant|> \nlymphocyte & count & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nI-131 therapy for hyperthyroidism <|end|>\n<|assistant|> \n(I-131 | radioiodine) & therapy & hyperthyroidism <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG treatment for bladder cancer <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & treatment & (\"bladder cancer\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClopidogrel after coronary stent <|end|>\n<|assistant|> \n(clopidogrel | Plavix) & (\"coronary stent\" | PCI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFNA of thyroid nodules <|end|>\n<|assistant|> \n(FNA | \"fine needle aspiration\") & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in surgical pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (surgical | pain) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrevention of contrast nephropathy <|end|>\n<|assistant|> \nprevention & (contrast | dye) & nephropathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWarfarin use in pregnancy <|end|>\n<|assistant|> \nwarfarin & use & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy for small bowel Crohn's <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS for anesthetic depth <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (anesthetic | anesthesia) & depth <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGlucocorticoid replacement for adrenal insufficiency <|end|>\n<|assistant|> \n(glucocorticoid | hydrocortisone) & replacement & (\"adrenal insufficiency\" | Addison''s) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndotracheal tube cuff pressure management <|end|>\n<|assistant|> \n(endotracheal | ET) & tube & cuff & pressure & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTreatment of C. difficile infection <|end|>\n<|assistant|> \ntreatment & (\"C. difficile\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic techniques for GI bleeding <|end|>\n<|assistant|> \nendoscopic & techniques & (\"GI bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCT for diagnosis of PE <|end|>\n<|assistant|> \n(CT | \"computed tomography\") & diagnosis & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressor selection in septic shock <|end|>\n<|assistant|> \n(vasopressor | pressor) & selection & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary NGAL for AKI <|end|>\n<|assistant|> \n(urinary | uNGAL) & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimizing PEEP in ARDS <|end|>\n<|assistant|> \n(PEEP | \"positive end-expiratory pressure\") & optimizing & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril valsartan for HF <|end|>\n<|assistant|> \n(sacubitril valsartan | Entresto) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThrombocytopenia in HELLP syndrome <|end|>\n<|assistant|> \nthrombocytopenia & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMonitoring INR on warfarin <|end|>\n<|assistant|> \nmonitoring & (INR | \"international ratio\") & warfarin <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrenatal diagnosis of CDH <|end|>\n<|assistant|> \n(prenatal | fetal) & diagnosis & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic-assisted colectomy for cancer <|end|>\n<|assistant|> \n(robotic-assisted | robotic) & colectomy & cancer <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as biomarker for anti-TNF response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & biomarker & (\"anti-TNF\" | biologics) & response <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDuration of DAPT post-PCI <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (post-PCI | \"after percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to discontinue antibiotics <|end|>\n<|assistant|> \nprocalcitonin & discontinue & antibiotics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone to prevent preterm delivery <|end|>\n<|assistant|> \nprogesterone & prevent & (\"preterm delivery\" | PTB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSedation protocols for AWS <|end|>\n<|assistant|> \nsedation & protocols & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEchocardiographic detection of pericardial effusion <|end|>\n<|assistant|> \n(echocardiographic | echo) & detection & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntra-articular corticosteroids for osteoarthritis <|end|>\n<|assistant|> \n(intra-articular | IA) & corticosteroids & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTiming of laparoscopic cholecystectomy in acute setting <|end|>\n<|assistant|> \ntiming & laparoscopic & cholecystectomy & acute <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFerritin as diagnostic marker for HLH <|end|>\n<|assistant|> \nferritin & diagnostic & marker & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nImpact of cuff size on BP accuracy <|end|>\n<|assistant|> \ncuff & size & (BP | \"blood pressure\") & accuracy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound evaluation of fetal growth restriction <|end|>\n<|assistant|> \nultrasound & evaluation & (\"fetal growth restriction\" | IUGR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibition for LDL reduction <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (LDL | cholesterol) & reduction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D in osteoporosis management <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & osteoporosis & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGuidelines for hyponatremia correction <|end|>\n<|assistant|> \nguidelines & hyponatremia & correction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTranscatheter ASD closure outcomes <|end|>\n<|assistant|> \ntranscatheter & (ASD | \"atrial septal defect\") & closure & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEmpiric antibiotic selection in bacteremia <|end|>\n<|assistant|> \n(empiric | antibiotics) & selection & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCardiac MRI accuracy for myocarditis <|end|>\n<|assistant|> \n(CMR | \"cardiac MRI\") & accuracy & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrognostic value of lactate in sepsis <|end|>\n<|assistant|> \n(prognostic | value) & lactate & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEfficacy of steroid injections for carpal tunnel <|end|>\n<|assistant|> \nefficacy & steroid & injections & (\"carpal tunnel\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal BP targets in ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & targets & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphopenia as predictor in COVID <|end|>\n<|assistant|> \nlymphopenia & predictor & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine treatment efficacy in Graves' disease <|end|>\n<|assistant|> \n(radioiodine | I-131) & efficacy & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG immunotherapy efficacy in bladder cancer <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & immunotherapy & efficacy & (\"bladder cancer\" | TCC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClopidogrel duration after drug-eluting stent <|end|>\n<|assistant|> \n(clopidogrel | Plavix) & duration & (DES | \"drug-eluting stent\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiagnostic accuracy of thyroid FNA <|end|>\n<|assistant|> \ndiagnostic & accuracy & thyroid & (FNA | \"fine needle aspiration\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPain control with liposomal bupivacaine <|end|>\n<|assistant|> \npain & control & (liposomal | Exparel) & bupivacaine <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStrategies to prevent contrast-induced nephropathy <|end|>\n<|assistant|> \nstrategies & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRisks of warfarin during pregnancy <|end|>\n<|assistant|> \nrisks & warfarin & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy diagnostic yield in small bowel Crohn's <|end|>\n<|assistant|> \n(capsule | VCE) & endoscopy & (diagnostic | yield) & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring to prevent awareness during anesthesia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & prevention & awareness & anesthesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone dosing in adrenal crisis <|end|>\n<|assistant|> \nhydrocortisone & dosing & (\"adrenal crisis\" | insufficiency) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndotracheal cuff pressure complications <|end|>\n<|assistant|> \n(endotracheal | ET) & cuff & pressure & complications <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFirst-line treatment for C. diff colitis <|end|>\n<|assistant|> \n(first-line | 1L) & treatment & (\"C. diff\" | CDI) & colitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdvanced endoscopic hemostasis techniques <|end|>\n<|assistant|> \nadvanced & endoscopic & hemostasis & techniques <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSensitivity of CT angiography for PE <|end|>\n<|assistant|> \nsensitivity & (CTA | \"CT angiography\") & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNorepinephrine vs vasopressin in septic shock <|end|>\n<|assistant|> \n(norepinephrine | noradrenaline) & (vasopressin | ADH) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive value of urinary NGAL for AKI <|end|>\n<|assistant|> \n(predictive | value) & (urinary | uNGAL) & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndividualized PEEP setting in ARDS <|end|>\n<|assistant|> \nindividualized & (PEEP | \"positive end-expiratory pressure\") & setting & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMortality benefit of sacubitril/valsartan in HF <|end|>\n<|assistant|> \nmortality & benefit & (sacubitril/valsartan | Entresto) & (HF | \"heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet transfusion thresholds in HELLP <|end|>\n<|assistant|> \n(platelet | PLT) & transfusion & thresholds & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFrequency of INR monitoring <|end|>\n<|assistant|> \nfrequency & (INR | \"international ratio\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAccuracy of fetal MRI for CDH <|end|>\n<|assistant|> \naccuracy & (fetal | prenatal) & (MRI | \"magnetic resonance\") & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOncologic outcomes of robotic colectomy <|end|>\n<|assistant|> \noncologic & outcomes & robotic & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP as predictor of response to TNF inhibitors <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & predictor & response & (\"TNF inhibitors\" | biologics) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal duration of DAPT after DES <|end|>\n<|assistant|> \nduration & (DAPT | \"dual antiplatelet therapy\") & (DES | \"drug-eluting stent\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin to guide antibiotic duration <|end|>\n<|assistant|> \nprocalcitonin & guide & antibiotics & duration <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProgesterone formulations for preterm prevention <|end|>\n<|assistant|> \nprogesterone & formulations & prevention & preterm <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBenzodiazepine-based sedation for AWS <|end|>\n<|assistant|> \n(benzodiazepine | BZD) & sedation & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nQuantification of pericardial effusion by echo <|end|>\n<|assistant|> \nquantification & (\"pericardial effusion\" | fluid) & (echo | echocardiography) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSteroid injections vs surgery for carpal tunnel <|end|>\n<|assistant|> \nsteroid & injections & surgery & (\"carpal tunnel\" | CTS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly vs delayed cholecystectomy in mild pancreatitis <|end|>\n<|assistant|> \n(early | delayed) & cholecystectomy & mild & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiagnostic criteria for HLH including ferritin <|end|>\n<|assistant|> \n(diagnostic | criteria) & (HLH | \"hemophagocytic lymphohistiocytosis\") & ferritin <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEffect of cuff size on hypertension diagnosis <|end|>\n<|assistant|> \ncuff & size & (hypertension | BP) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound predictors of adverse outcome in IUGR <|end|>\n<|assistant|> \nultrasound & predictors & (adverse | outcome) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of PCSK9 inhibitors <|end|>\n<|assistant|> \n(cost-effectiveness | economic) & (PCSK9 | inhibitor) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and calcium for osteoporosis prevention <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & calcium & osteoporosis & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRapid vs slow correction of hyponatremia <|end|>\n<|assistant|> \n(rapid | slow) & correction & hyponatremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLong-term outcomes of percutaneous ASD closure <|end|>\n<|assistant|> \n\"long term\" & outcomes & percutaneous & (ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAntibiotic resistance in Gram-negative bacteremia <|end|>\n<|assistant|> \n(antibiotic | resistance) & (\"Gram-negative\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT1 and T2 mapping in cardiac MRI for myocarditis <|end|>\n<|assistant|> \n(T1 | T2) & mapping & (CMR | \"cardiac MRI\") & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance as goal in sepsis resuscitation <|end|>\n<|assistant|> \n(lactate | clearance) & goal & sepsis & resuscitation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltrasound-guided vs landmark steroid injection for CTS <|end|>\n<|assistant|> \n(ultrasound-guided | landmark) & steroid & injection & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntensive vs conventional BP control in ICH <|end|>\n<|assistant|> \n(intensive | conventional) & (BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLymphocyte-to-CRP ratio in COVID prognosis <|end|>\n<|assistant|> \n(lymphocyte | CRP) & ratio & (COVID | SARS-CoV-2) & prognosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs antithyroid drugs for Graves' disease <|end|>\n<|assistant|> \n(radioiodine | I-131) & (antithyroid | methimazole) & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG maintenance therapy for high-risk NMIBC <|end|>\n<|assistant|> \n(BCG | \"Bacillus Calmette-Guérin\") & maintenance & therapy & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor vs clopidogrel after acute coronary syndrome <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & (clopidogrel | Plavix) & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMolecular testing of thyroid FNA samples <|end|>\n<|assistant|> \n(molecular | genetic) & testing & thyroid & (FNA | \"fine needle aspiration\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in enhanced recovery after surgery <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (\"enhanced recovery\" | ERAS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSodium bicarbonate for contrast nephropathy prevention <|end|>\n<|assistant|> \n(sodium bicarbonate | NaHCO3) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLMWH vs warfarin in pregnancy <|end|>\n<|assistant|> \n(LMWH | \"low molecular weight heparin\") & warfarin & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCapsule endoscopy vs MRE for small bowel Crohn's <|end|>\n<|assistant|> \n(capsule | VCE) & (MRE | \"magnetic resonance enterography\") & (\"small bowel\" | SB) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and postoperative cognitive dysfunction <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (POCD | \"postoperative cognitive dysfunction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStress-dose steroids in septic shock with adrenal insufficiency <|end|>\n<|assistant|> \n(stress-dose | steroids) & (\"septic shock\" | sepsis) & (\"adrenal insufficiency\" | cortisol) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutomated cuff pressure controllers in mechanical ventilation <|end|>\n<|assistant|> \nautomated & cuff & pressure & controllers & (\"mechanical ventilation\" | MV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal microbiota transplantation for recurrent C. diff <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOver-the-scope clips for GI bleeding <|end|>\n<|assistant|> \n(OTSC | \"over-the-scope clips\") & (\"GI bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer combined with clinical probability for PE <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & (\"clinical probability\" | Wells) & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II for vasodilatory shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (vasodilatory | septic) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCombination biomarkers for AKI prediction <|end|>\n<|assistant|> \ncombination & biomarkers & (AKI | \"acute kidney injury\") & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEsophageal pressure-guided PEEP in ARDS <|end|>\n<|assistant|> \n(esophageal | pressure) & guided & (PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HF with preserved ejection fraction <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (HFpEF | \"heart failure with preserved ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet count recovery after delivery in HELLP <|end|>\n<|assistant|> \n(platelet | PLT) & count & recovery & delivery & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPoint-of-care INR testing <|end|>\n<|assistant|> \n(point-of-care | POC) & (INR | \"international ratio\") & testing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI predictors of outcome in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & predictors & outcome & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic colectomy for cancer <|end|>\n<|assistant|> \n(robotic | laparoscopic) & colectomy & cancer <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSerial CRP measurements to monitor biologic response <|end|>\n<|assistant|> \n(serial | repeated) & (CRP | \"C-reactive protein\") & measurements & monitor & (biologics | response) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nShort vs long DAPT after high-bleeding-risk PCI <|end|>\n<|assistant|> \n(short | long) & (DAPT | \"dual antiplatelet therapy\") & (\"high bleeding risk\" | HBR) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics to predict antibiotic failure <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & predict & antibiotic & failure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nComparative effectiveness of progesterone formulations for PTB <|end|>\n<|assistant|> \n(comparative | effectiveness) & progesterone & formulations & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPhenobarbital vs benzodiazepines for AWS <|end|>\n<|assistant|> \n(phenobarbital | barbiturate) & (benzodiazepines | BZD) & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echocardiography for pericardial effusion <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & echocardiography & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet-rich plasma vs steroid injection for knee OA <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & steroid & injection & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy for mild gallstone pancreatitis <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & mild & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore for HLH diagnosis <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (HLH | \"hemophagocytic lymphohistiocytosis\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff size effect on masked hypertension <|end|>\n<|assistant|> \ncuff & size & effect & (\"masked hypertension\" | BP) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDoppler ultrasound parameters in fetal growth restriction <|end|>\n<|assistant|> \n(Doppler | ultrasound) & parameters & (\"fetal growth restriction\" | IUGR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and fracture risk in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (fracture | risk) & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressin vs norepinephrine in hyponatremic shock <|end|>\n<|assistant|> \n(vasopressin | ADH) & (norepinephrine | noradrenaline) & (hyponatremic | shock) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMinimally invasive ASD closure techniques <|end|>\n<|assistant|> \n(minimally invasive | percutaneous) & (ASD | \"atrial septal defect\") & closure & techniques <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRapid diagnostics in Gram-negative bacteremia <|end|>\n<|assistant|> \n(rapid | diagnostics) & (\"Gram-negative\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT1 mapping cutoff values in acute myocarditis <|end|>\n<|assistant|> \n(T1 | mapping) & (cutoff | values) & acute & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate-directed resuscitation in sepsis <|end|>\n<|assistant|> \n(lactate | clearance) & directed & resuscitation & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of steroid injections for CTS <|end|>\n<|assistant|> \n(cost-effectiveness | economic) & steroid & injections & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal systolic BP target in ICH evacuation <|end|>\n<|assistant|> \n(systolic | BP) & target & (ICH | \"intracerebral hemorrhage\") & evacuation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMachine learning models using lymphocyte data for COVID prognosis <|end|>\n<|assistant|> \n\"machine learning\" & models & lymphocyte & data & (COVID | SARS-CoV-2) & prognosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nQuality of life after radioiodine for Graves' disease <|end|>\n<|assistant|> \n(QoL | \"quality of life\") & after & (radioiodine | I-131) & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBCG-unresponsive NMIBC treatment options <|end|>\n<|assistant|> \n(BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") & treatment <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDe-escalation from ticagrelor to clopidogrel after ACS <|end|>\n<|assistant|> \n(de-escalation | switch) & (ticagrelor | Brilinta) & (clopidogrel | Plavix) & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyroSeq molecular testing for indeterminate thyroid nodules <|end|>\n<|assistant|> \n(ThyroSeq | molecular) & testing & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in outpatient surgery <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (outpatient | ambulatory) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStatin pretreatment for contrast nephropathy prevention <|end|>\n<|assistant|> \n(statin | HMG-CoA) & pretreatment & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDirect oral anticoagulants vs LMWH in pregnancy <|end|>\n<|assistant|> \n(DOACs | \"direct oral anticoagulants\") & (LMWH | \"low molecular weight heparin\") & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nArtificial intelligence in capsule endoscopy for Crohn's <|end|>\n<|assistant|> \n(AI | \"artificial intelligence\") & (capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS-guided anesthesia and long-term cancer outcomes <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & guided & anesthesia & (\"long term\" | survival) & cancer <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid dosing in septic shock with critical illness-related corticosteroid insufficiency <|end|>\n<|assistant|> \n(corticosteroid | hydrocortisone) & dosing & (\"septic shock\" | sepsis) & (CIRCI | \"critical illness-related corticosteroid insufficiency\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff pressure variability during patient transport <|end|>\n<|assistant|> \ncuff & pressure & variability & (\"patient transport\" | transport) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFrozen vs fresh FMT for recurrent C. diff <|end|>\n<|assistant|> \n(frozen | fresh) & (FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (\"C. diff\" | CDI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHemospray vs conventional methods for peptic ulcer bleeding <|end|>\n<|assistant|> \n(Hemospray | TC-325) & (conventional | methods) & (\"peptic ulcer\" | ulcer) & bleeding <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAge-adjusted D-dimer for PE exclusion in elderly <|end|>\n<|assistant|> \n(age-adjusted | modified) & (D-dimer | \"fibrin fragment\") & (PE | \"pulmonary embolism\") & exclusion & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in high-output heart failure <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (\"high output\" | HF) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTIMP-2•IGFBP7 for AKI risk stratification <|end|>\n<|assistant|> \n(TIMP-2•IGFBP7 | NephroCheck) & (AKI | \"acute kidney injury\") & (risk | stratification) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndividualized PEEP using electrical impedance tomography <|end|>\n<|assistant|> \nindividualized & (PEEP | \"positive end-expiratory pressure\") & (EIT | \"electrical impedance tomography\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HF with mid-range EF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (HFmrEF | \"heart failure with mid-range ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPlatelet function testing in HELLP syndrome <|end|>\n<|assistant|> \n(platelet | function) & testing & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPatient self-testing of INR <|end|>\n<|assistant|> \n(patient | self) & testing & (INR | \"international ratio\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume quantification in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | quantification) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic surgery learning curve for colectomy <|end|>\n<|assistant|> \nrobotic & surgery & (\"learning curve\" | training) & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP velocity to predict anti-TNF primary non-response <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & velocity & predict & (\"anti-TNF\" | biologics) & (\"non response\" | failure) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGenotype-guided DAPT duration after PCI <|end|>\n<|assistant|> \n(genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") & duration & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin change to guide antibiotic therapy in sepsis <|end|>\n<|assistant|> \n(PCT | procalcitonin) & change & guide & antibiotics & therapy & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVaginal progesterone vs cerclage for preterm birth prevention <|end|>\n<|assistant|> \nvaginal & progesterone & (cerclage | suture) & prevention & (\"preterm birth\" | PTB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine adjunct for severe AWS <|end|>\n<|assistant|> \nketamine & adjunct & severe & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance for pericardiocentesis <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & guidance & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPRP injection for thumb base osteoarthritis <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & injection & (\"thumb base\" | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive factors for choledocholithiasis in mild gallstone pancreatitis <|end|>\n<|assistant|> \npredictive & factors & (choledocholithiasis | stones) & mild & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore validation in adult HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & validation & (adult | HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEffect of cuff size on nocturnal hypertension <|end|>\n<|assistant|> \ncuff & size & effect & (nocturnal | hypertension) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery Doppler in early-onset IUGR <|end|>\n<|assistant|> \n(umbilical | UA) & Doppler & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in statin intolerance <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (statin | HMG-CoA) & intolerance <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and falls in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & falls & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVasopressin in catecholamine-resistant septic shock <|end|>\n<|assistant|> \n(vasopressin | ADH) & (catecholamine-resistant | refractory) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDevice closure vs surgery for ASD <|end|>\n<|assistant|> \n(device | percutaneous) & closure & surgery & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRapid AST in bloodstream infections <|end|>\n<|assistant|> \n(rapid | accelerated) & (AST | \"antimicrobial susceptibility testing\") & (\"bloodstream infections\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nExtracellular volume fraction in chronic myocarditis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & fraction & chronic & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance vs ScvO2 in sepsis resuscitation <|end|>\n<|assistant|> \n(lactate | clearance) & (ScvO2 | \"central venous oxygen saturation\") & sepsis & resuscitation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-utility analysis of steroid injections for CTS <|end|>\n<|assistant|> \n(cost-utility | economic) & analysis & steroid & injections & (CTS | \"carpal tunnel syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP variability in ICH outcome <|end|>\n<|assistant|> \n(systolic | BP) & variability & (ICH | \"intracerebral hemorrhage\") & outcome <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDeep learning using lymphocyte images for COVID severity <|end|>\n<|assistant|> \n\"deep learning\" & lymphocyte & images & (COVID | SARS-CoV-2) & severity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs surgery for toxic adenoma <|end|>\n<|assistant|> \n(radioiodine | I-131) & surgery & (\"toxic adenoma\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntravesical gemcitabine/docetaxel for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(intravesical | bladder) & (gemcitabine/docetaxel | chemotherapy) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDe-escalation DAPT strategies after ACS <|end|>\n<|assistant|> \nde-escalation & (DAPT | \"dual antiplatelet therapy\") & strategies & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMolecular classifier for indeterminate thyroid cytology <|end|>\n<|assistant|> \n(molecular | classifier) & (indeterminate | Bethesda) & thyroid & cytology <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in ERAS protocols <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (ERAS | \"enhanced recovery after surgery\") & protocols <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRemote ischemic preconditioning for contrast nephropathy <|end|>\n<|assistant|> \n(remote | ischemic) & preconditioning & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDOACs in mechanical heart valves <|end|>\n<|assistant|> \n(DOACs | \"direct oral anticoagulants\") & (\"mechanical heart valves\" | MHV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAI-assisted reading of capsule endoscopy in Crohn's <|end|>\n<|assistant|> \n(AI | \"artificial intelligence\") & assisted & reading & (capsule | VCE) & endoscopy & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and delirium after cardiac surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (delirium | confusion) & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid tapering in septic shock recovery <|end|>\n<|assistant|> \n(corticosteroid | steroid) & tapering & (\"septic shock\" | sepsis) & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContinuous cuff pressure control systems <|end|>\n<|assistant|> \ncontinuous & cuff & pressure & control & systems <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOral vs encapsulated FMT for recurrent CDI <|end|>\n<|assistant|> \n(oral | encapsulated) & (FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHemostatic powder vs clip for malignant GI bleeding <|end|>\n<|assistant|> \n(hemostatic | powder) & (clip | endoclip) & (malignant | cancer) & (\"GI bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nYEARS algorithm for PE diagnosis <|end|>\n<|assistant|> \n(YEARS | algorithm) & (PE | \"pulmonary embolism\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in distributive shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (distributive | vasodilatory) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI prediction in sepsis <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & prediction & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT-guided PEEP titration in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & guided & (PEEP | \"positive end-expiratory pressure\") & titration & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in cancer therapy-related cardiac dysfunction <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (CTRCD | \"cancer therapy-related cardiac dysfunction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThrombopoietin agonists in HELLP syndrome <|end|>\n<|assistant|> \n(thrombopoietin | TPO) & agonists & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPharmacist-managed INR clinics <|end|>\n<|assistant|> \npharmacist & managed & (INR | \"international ratio\") & clinics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI liver position in CDH prognosis <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"liver position\" | herniation) & prognosis & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy cost-effectiveness <|end|>\n<|assistant|> \nrobotic & colectomy & (cost-effectiveness | economic) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP-based treat-to-target in IBD <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (\"treat to target\" | T2T) & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 genotype and DAPT outcomes <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & (DAPT | \"dual antiplatelet therapy\") & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope for antibiotic stewardship <|end|>\n<|assistant|> \n(PCT | procalcitonin) & slope & (antibiotic | stewardship) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n17-OHPC vs vaginal progesterone for PTB prevention <|end|>\n<|assistant|> \n(17-OHPC | \"hydroxyprogesterone caproate\") & vaginal & progesterone & prevention & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDexmedetomidine for AWS with delirium <|end|>\n<|assistant|> \n(dexmedetomidine | Precedex) & (AWS | \"alcohol withdrawal syndrome\") & delirium <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo vs CT for pericardial effusion characterization <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & (CT | \"computed tomography\") & (\"pericardial effusion\" | fluid) & characterization <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPRP vs hyaluronic acid for knee OA <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & (hyaluronic | HA) & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive model for choledocholithiasis in acute biliary pancreatitis <|end|>\n<|assistant|> \npredictive & model & (choledocholithiasis | stones) & acute & (biliary | gallstone) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in secondary HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (secondary | HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff size impact on hypertension prevalence <|end|>\n<|assistant|> \ncuff & size & impact & (hypertension | prevalence) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus Doppler in early IUGR <|end|>\n<|assistant|> \n(ductus venosus | DV) & Doppler & (early | onset) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in heterozygous FH <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (heterozygous | HeFH) & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and muscle strength in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (muscle | strength) & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelepressin vs norepinephrine in septic shock <|end|>\n<|assistant|> \n(selepressin | vasopressin) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer vs Gore device for ASD closure <|end|>\n<|assistant|> \n(Amplatzer | Gore) & device & (ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRapid multiplex PCR for bloodstream infections <|end|>\n<|assistant|> \n(rapid | multiplex) & (PCR | \"polymerase chain reaction\") & (\"bloodstream infections\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNative T1 mapping in acute myocarditis diagnosis <|end|>\n<|assistant|> \n(native | T1) & mapping & acute & myocarditis & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in septic shock <|end|>\n<|assistant|> \n(lactate | clearance) & time & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMarkov model cost-effectiveness of CTS treatments <|end|>\n<|assistant|> \n(Markov | model) & (cost-effectiveness | economic) & (CTS | \"carpal tunnel syndrome\") & treatments <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOptimal BP trajectory in ICH recovery <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & trajectory & (ICH | \"intracerebral hemorrhage\") & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nConvolutional neural network for lymphocyte classification in COVID <|end|>\n<|assistant|> \n(CNN | \"convolutional neural network\") & lymphocyte & classification & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine ablation vs antithyroid drugs for toxic MNG <|end|>\n<|assistant|> \n(radioiodine | I-131) & ablation & (antithyroid | methimazole) & (\"toxic MNG\" | multinodular) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNadofaragene firadenovec for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(nadofaragene | firadenovec) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after short DAPT <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (short | brief) & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for indeterminate thyroid nodules <|end|>\n<|assistant|> \n(Afirma | GSC) & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in total joint arthroplasty <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TJA | \"total joint arthroplasty\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTheophylline for contrast nephropathy prevention <|end|>\n<|assistant|> \n(theophylline | aminophylline) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDOAC reversal agents in bleeding <|end|>\n<|assistant|> \n(DOACs | \"direct oral anticoagulants\") & (reversal | antidote) & bleeding <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDeep learning for small bowel lesion detection in capsule endoscopy <|end|>\n<|assistant|> \n\"deep learning\" & (\"small bowel\" | SB) & lesion & detection & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and postoperative nausea <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (PONV | \"postoperative nausea and vomiting\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid duration in septic shock <|end|>\n<|assistant|> \n(corticosteroid | hydrocortisone) & duration & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutomated vs manual cuff pressure control <|end|>\n<|assistant|> \n(automated | manual) & cuff & pressure & control <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT delivery methods for recurrent CDI <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (delivery | methods) & (recurrent | relapse) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCombination endoscopic therapy for peptic ulcer bleeding <|end|>\n<|assistant|> \ncombination & endoscopic & therapy & (\"peptic ulcer\" | ulcer) & bleeding <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPEGeD algorithm for PE diagnosis <|end|>\n<|assistant|> \n(PEGeD | algorithm) & (PE | \"pulmonary embolism\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in vasoplegia after cardiac surgery <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (vasoplegia | shock) & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary CCL14 for persistent AKI <|end|>\n<|assistant|> \n(urinary | CCL14) & (persistent | severe) & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in obese ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & (obese | obesity) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in anthracycline-induced cardiotoxicity <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (anthracycline | doxorubicin) & cardiotoxicity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRomiplostim in HELLP syndrome <|end|>\n<|assistant|> \n(romiplostim | Nplate) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTelemedicine for INR management <|end|>\n<|assistant|> \ntelemedicine & (INR | \"international ratio\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI O/E LHR in CDH survival <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"O/E LHR\" | \"observed to expected lung area to head circumference ratio\") & survival & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLearning curve robotic colectomy <|end|>\n<|assistant|> \n(learning curve | training) & robotic & colectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP correlation with endoscopic activity in Crohn's <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & correlation & (endoscopic | activity) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPharmacogenomic-guided DAPT after PCI <|end|>\n<|assistant|> \n(pharmacogenomic | genotype) & guided & (DAPT | \"dual antiplatelet therapy\") & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics in COVID pneumonia <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (COVID | SARS-CoV-2) & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCombined progesterone and cerclage for PTB prevention <|end|>\n<|assistant|> \n(combined | combination) & progesterone & cerclage & prevention & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPhenobarbital protocol for severe AWS <|end|>\n<|assistant|> \n(phenobarbital | barbiturate) & protocol & severe & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance vs fluoroscopy for pericardiocentesis <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & guidance & (fluoroscopy | X-ray) & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPRP injection for erosive hand OA <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & injection & (erosive | hand) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive score for ERCP need in gallstone pancreatitis <|end|>\n<|assistant|> \npredictive & score & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & need & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHLH-2004 vs HScore diagnostic accuracy <|end|>\n<|assistant|> \n(HLH-2004 | HScore) & (diagnostic | accuracy) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCuff size and masked uncontrolled hypertension <|end|>\n<|assistant|> \ncuff & size & (\"masked uncontrolled\" | hypertension) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in late-onset IUGR <|end|>\n<|assistant|> \n(cerebroplacental | CPR) & ratio & (\"late onset\" | late) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in homozygous FH <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (homozygous | HoFH) & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sarcopenia in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & sarcopenia & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs norepinephrine in hepatorenal syndrome <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (norepinephrine | noradrenaline) & (\"hepatorenal syndrome\" | HRS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDevice closure for secundum ASD <|end|>\n<|assistant|> \n(device | percutaneous) & closure & (\"secundum ASD\" | ASD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFilmArray BCID for rapid pathogen identification <|end|>\n<|assistant|> \n(FilmArray | BCID) & (rapid | pathogen) & identification & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT1 mapping in chronic inflammatory cardiomyopathy <|end|>\n<|assistant|> \n(T1 | mapping) & chronic & (inflammatory | myocarditis) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis mortality <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & sepsis & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDiscrete event simulation for CTS treatment pathways <|end|>\n<|assistant|> \n(DES | \"discrete event simulation\") & (CTS | \"carpal tunnel syndrome\") & (treatment | pathways) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBP variability and hematoma expansion in ICH <|end|>\n<|assistant|> \n(BP | \"blood pressure\") & variability & (\"hematoma expansion\" | growth) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransfer learning for COVID lymphocyte analysis <|end|>\n<|assistant|> \n\"transfer learning\" & (COVID | SARS-CoV-2) & lymphocyte & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine vs surgery for toxic nodular goiter <|end|>\n<|assistant|> \n(radioiodine | I-131) & surgery & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntravesical nadofaragene for high-risk NMIBC <|end|>\n<|assistant|> \n(intravesical | bladder) & (nadofaragene | firadenovec) & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after 1-month DAPT <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"1 month\" | short) & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT/ThyraMIR for indeterminate thyroid nodules <|end|>\n<|assistant|> \n(ThyGeNEXT | ThyraMIR) & (indeterminate | Bethesda) & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in abdominal wall reconstruction <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (\"abdominal wall\" | hernia) & reconstruction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAllopurinol for contrast nephropathy prevention <|end|>\n<|assistant|> \n(allopurinol | Zyloprim) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet alfa for DOAC reversal <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (DOACs | \"direct oral anticoagulants\") & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCNN for ulcer detection in capsule endoscopy <|end|>\n<|assistant|> \n(CNN | \"convolutional neural network\") & ulcer & detection & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring and opioid consumption <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (opioid | consumption) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid weaning in septic shock recovery <|end|>\n<|assistant|> \n(corticosteroid | hydrocortisone) & weaning & (\"septic shock\" | sepsis) & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSmart cuff pressure regulation systems <|end|>\n<|assistant|> \n(smart | automated) & cuff & pressure & regulation & systems <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT via colonoscopy vs capsules for recurrent CDI <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (colonoscopy | capsules) & (recurrent | relapse) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOverStitch for refractory GI bleeding <|end|>\n<|assistant|> \n(OverStitch | endoscopic) & (refractory | resistant) & (\"GI bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n4-level PE diagnostic algorithm <|end|>\n<|assistant|> \n(4-level | algorithm) & (PE | \"pulmonary embolism\") & diagnostic <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in high-dose vasopressor shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (\"high dose\" | vasopressor) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTIMP2•IGFBP7 for AKI staging <|end|>\n<|assistant|> \n(TIMP2•IGFBP7 | NephroCheck) & (AKI | \"acute kidney injury\") & staging <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for regional ventilation optimization in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (regional | ventilation) & optimization & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in cardiotoxicity prevention <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (cardiotoxicity | prevention) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEltrombopag in HELLP syndrome <|end|>\n<|assistant|> \n(eltrombopag | Promacta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMobile app for INR self-management <|end|>\n<|assistant|> \n(mobile | app) & (INR | \"international ratio\") & (self-management | monitoring) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total lung volume in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total lung volume\" | TLV) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic colectomy for rectal cancer <|end|>\n<|assistant|> \nrobotic & colectomy & (\"rectal cancer\" | rectum) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP-based de-escalation in Crohn's <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & (de-escalation | reduction) & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGenotype-guided vs standard DAPT <|end|>\n<|assistant|> \n(genotype | guided) & standard & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin velocity in bacterial coinfection <|end|>\n<|assistant|> \n(PCT | procalcitonin) & velocity & (bacterial | coinfection) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n17-OHPC vs placebo for PTB prevention <|end|>\n<|assistant|> \n(17-OHPC | \"hydroxyprogesterone caproate\") & placebo & prevention & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDexmedetomidine for AWS with benzodiazepine resistance <|end|>\n<|assistant|> \n(dexmedetomidine | Precedex) & (AWS | \"alcohol withdrawal syndrome\") & (resistance | refractory) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo vs MRI for pericardial effusion <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & (MRI | \"magnetic resonance\") & (\"pericardial effusion\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPRP injection for base of thumb OA <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & injection & (\"base of thumb\" | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive model for common bile duct stones in gallstone pancreatitis <|end|>\n<|assistant|> \npredictive & model & (\"common bile duct\" | CBD) & stones & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in macrophage activation syndrome <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (MAS | \"macrophage activation syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLarge cuff effect on hypertension diagnosis rates <|end|>\n<|assistant|> \n(large | cuff) & size & effect & (hypertension | diagnosis) & rates <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery PI in IUGR management <|end|>\n<|assistant|> \n(umbilical | UA) & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in pediatric FH <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & pediatric & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and physical performance in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (\"physical performance\" | function) & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelepressin in septic shock with ARDS <|end|>\n<|assistant|> \n(selepressin | vasopressin) & (\"septic shock\" | sepsis) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDevice closure for fenestrated Fontan <|end|>\n<|assistant|> \n(device | percutaneous) & closure & (\"fenestrated Fontan\" | Fontan) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid bacteremia diagnosis <|end|>\n<|assistant|> \n(T2Bacteria | panel) & rapid & diagnosis & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV mapping in myocarditis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & mapping & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/clearance ratio in septic shock <|end|>\n<|assistant|> \n(lactate | clearance) & ratio & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAgent-based modeling for CTS treatment access <|end|>\n<|assistant|> \n\"agent based modeling\" & (CTS | \"carpal tunnel syndrome\") & (treatment | access) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltra-early BP reduction in ICH <|end|>\n<|assistant|> \n(ultra-early | immediate) & (BP | \"blood pressure\") & reduction & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFederated learning for COVID lymphocyte data <|end|>\n<|assistant|> \n\"federated learning\" & (COVID | SARS-CoV-2) & lymphocyte & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine for toxic adenoma vs MNG <|end|>\n<|assistant|> \n(radioiodine | I-131) & (\"toxic adenoma\" | MNG | \"multinodular goiter\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrasugrel monotherapy after PCI <|end|>\n<|assistant|> \n(prasugrel | Effient) & monotherapy & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for thyroid nodules <|end|>\n<|assistant|> \n(RosettaGX | Reveal) & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast reconstruction <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (\"breast reconstruction\" | mastectomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNebivolol for contrast nephropathy <|end|>\n<|assistant|> \n(nebivolol | Bystolic) & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for DOAC reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (DOACs | \"direct oral anticoagulants\") & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformer for capsule endoscopy analysis <|end|>\n<|assistant|> \n(ViT | \"vision transformer\") & (capsule | VCE) & endoscopy & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS and postoperative cognitive function <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (postoperative | cognitive) & function <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroid duration impact on sepsis outcomes <|end|>\n<|assistant|> \n(corticosteroid | hydrocortisone) & duration & impact & (sepsis | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nClosed-loop cuff pressure control <|end|>\n<|assistant|> \n(closed-loop | automated) & cuff & pressure & control <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT for recurrent CDI in IBD patients <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\") & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum therapy for GI perforations <|end|>\n<|assistant|> \n(endoscopic | vacuum) & therapy & (\"GI perforations\" | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPregnancy-adapted YEARS algorithm <|end|>\n<|assistant|> \n(pregnancy | adapted) & (YEARS | algorithm) & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in burn shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (\"burn shock\" | burns) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary CCL14 for AKI progression <|end|>\n<|assistant|> \n(urinary | CCL14) & (AKI | \"acute kidney injury\") & progression <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for personalized PEEP in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & personalized & (PEEP | \"positive end-expiratory pressure\") & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in HER2-positive cardiotoxicity <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (HER2 | trastuzumab) & cardiotoxicity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP syndrome <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWearable INR monitors <|end|>\n<|assistant|> \n(wearable | device) & (INR | \"international ratio\") & monitors <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI quantifies liver herniation in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & quantifies & (\"liver herniation\" | herniation) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic vs open rectal cancer surgery <|end|>\n<|assistant|> \n(robotic | open) & (\"rectal cancer\" | rectum) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCRP-guided anti-TNF dosing in Crohn's <|end|>\n<|assistant|> \n(CRP | \"C-reactive protein\") & guided & (\"anti-TNF\" | biologics) & dosing & (\"Crohn''s disease\" | CD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing cost-effectiveness in PCI <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (cost-effectiveness | economic) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life in COVID-19 ARDS <|end|>\n<|assistant|> \n(PCT | procalcitonin) & (\"half life\" | kinetics) & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCombined screening for PTB prevention <|end|>\n<|assistant|> \ncombined & screening & prevention & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine infusion for refractory AWS <|end|>\n<|assistant|> \nketamine & infusion & (refractory | resistant) & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo guidance for complex pericardial effusions <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & guidance & (complex | loculated) & (\"pericardial effusions\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPRP injection for first CMC joint OA <|end|>\n<|assistant|> \n(PRP | \"platelet rich plasma\") & injection & (\"first CMC joint\" | thumb) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCholangitis prediction in gallstone pancreatitis <|end|>\n<|assistant|> \ncholangitis & prediction & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in rheumatology patients <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (rheumatology | autoimmune) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThigh cuff for BP measurement in obesity <|end|>\n<|assistant|> \n(thigh | cuff) & (BP | \"blood pressure\") & measurement & obesity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in late IUGR <|end|>\n<|assistant|> \n(MCA | \"middle cerebral artery\") & Doppler & (late | onset) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in children with FH <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & children & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and frailty in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & frailty & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for hepatorenal syndrome type 1 <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (\"hepatorenal syndrome\" | HRS) & type <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDevice closure for PFO with cryptogenic stroke <|end|>\n<|assistant|> \n(device | percutaneous) & closure & (PFO | \"patent foramen ovale\") & (\"cryptogenic stroke\" | stroke) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for bloodstream infections <|end|>\n<|assistant|> \n(Accelerate | Pheno) & (\"bloodstream infections\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nNative T1 in acute vs chronic myocarditis <|end|>\n<|assistant|> \n(native | T1) & mapping & (acute | chronic) & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate index in sepsis severity <|end|>\n<|assistant|> \n(lactate | index) & sepsis & severity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics modeling for CTS care pathways <|end|>\n<|assistant|> \n\"system dynamics\" & modeling & (CTS | \"carpal tunnel syndrome\") & (care | pathways) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP thresholds in ICH evacuation <|end|>\n<|assistant|> \n(systolic | BP) & thresholds & (ICH | \"intracerebral hemorrhage\") & evacuation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSemi-supervised learning for COVID lymphocyte <|end|>\n<|assistant|> \n\"semi supervised learning\" & (COVID | SARS-CoV-2) & lymphocyte <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose optimization for toxic nodular goiter <|end|>\n<|assistant|> \n(radioiodine | I-131) & dose & optimization & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVicinium for BCG-unresponsive CIS <|end|>\n<|assistant|> \n(vicinium | oportuzumab) & (BCG-unresponsive | resistant) & (CIS | \"carcinoma in situ\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyroSeq v3 for Bethesda III nodules <|end|>\n<|assistant|> \n(ThyroSeq | v3) & (\"Bethesda III\" | indeterminate) & nodules <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in panniculectomy <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & panniculectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRemote ischemic conditioning for CIN prevention <|end|>\n<|assistant|> \n(remote | ischemic) & conditioning & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal <|end|>\n<|assistant|> \n(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransformer models for capsule endoscopy video analysis <|end|>\n<|assistant|> \n(transformer | models) & (capsule | VCE) & endoscopy & video & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS and emergence delirium <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (emergence | delirium) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and ICU-acquired weakness <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (\"ICU acquired weakness\" | myopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntelligent cuff pressure management <|end|>\n<|assistant|> \n(intelligent | AI) & cuff & pressure & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT for recurrent CDI in immunocompromised <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (recurrent | relapse) & (CDI | \"C. diff infection\") & (immunocompromised | immunosuppressed) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic suturing for GI defects <|end|>\n<|assistant|> \nendoscopic & suturing & (\"GI defects\" | perforation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGeneva score revised for PE <|end|>\n<|assistant|> \n(Geneva | score) & revised & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neurogenic shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (neurogenic | spinal) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUrinary [TIMP-2]•[IGFBP7] for AKI subphenotypes <|end|>\n<|assistant|> \n(urinary | [TIMP-2]•[IGFBP7]) & (AKI | \"acute kidney injury\") & subphenotypes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral lung injury <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (\"unilateral lung\" | injury) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in radiation-induced cardiotoxicity <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (radiation | radiotherapy) & cardiotoxicity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag in HELLP <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCloud-based INR monitoring <|end|>\n<|assistant|> \n(cloud | digital) & (INR | \"international ratio\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-head ratio in CDH prognosis <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to head ratio\" | LHR) & prognosis & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer surgery outcomes <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPoint-of-care CRP for IBD monitoring <|end|>\n<|assistant|> \n(point-of-care | POC) & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of CYP2C19 testing in DAPT <|end|>\n<|assistant|> \n(cost-effectiveness | economic) & (CYP2C19 | genotype) & testing & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin clearance in ventilator-associated pneumonia <|end|>\n<|assistant|> \n(PCT | procalcitonin) & clearance & (\"ventilator associated\" | VAP) & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCombined biomarkers for PTB prediction <|end|>\n<|assistant|> \ncombined & biomarkers & prediction & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine vs benzodiazepines for severe AWS <|end|>\n<|assistant|> \nketamine & (benzodiazepines | BZD) & severe & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed models for pericardiocentesis planning <|end|>\n<|assistant|> \n(3D | printed) & models & pericardiocentesis & planning <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous conditioned plasma for hand OA <|end|>\n<|assistant|> \n(ACP | \"autologous conditioned plasma\") & (hand | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nERCP timing in gallstone pancreatitis with cholangitis <|end|>\n<|assistant|> \n(ERCP | \"endoscopic retrograde cholangiopancreatography\") & timing & (gallstone | biliary) & pancreatitis & cholangitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nModified HScore for secondary HLH <|end|>\n<|assistant|> \n(modified | adapted) & (HScore | \"HScore\") & (secondary | HLH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff selection <|end|>\n<|assistant|> \n(upper arm | circumference) & based & cuff & selection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio cutoff for IUGR <|end|>\n<|assistant|> \n(cerebroplacental | CPR) & ratio & cutoff & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in statin-associated muscle symptoms <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (statin | HMG-CoA) & (muscle | symptoms) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and cognition in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & cognition & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin albumin combination for HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & albumin & combination & (HRS | \"hepatorenal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer cribriform for multifenestrated ASD <|end|>\n<|assistant|> \n(Amplatzer | cribriform) & device & (multifenestrated | ASD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nQIAstat-Dx for sepsis diagnosis <|end|>\n<|assistant|> \n(QIAstat | Dx) & sepsis & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV fraction in cardiac sarcoidosis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & fraction & (\"cardiac sarcoid\" | sarcoidosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate-to-albumin ratio prognostic value <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & (prognostic | value) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCTS treatment pathways using discrete event simulation <|end|>\n<|assistant|> \n(CTS | \"carpal tunnel syndrome\") & (treatment | pathways) & (\"discrete event simulation\" | DES) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltra-early intensive BP lowering in ICH <|end|>\n<|assistant|> \n(ultra-early | immediate) & intensive & (BP | \"blood pressure\") & lowering & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte analysis <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPersonalized radioiodine dosing for Graves' disease <|end|>\n<|assistant|> \npersonalized & (radioiodine | I-131) & dosing & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(Instiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after STEMI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (STEMI | \"ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression Atlas for thyroid nodules <|end|>\n<|assistant|> \n(Afirma | Xpression) & Atlas & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap reconstruction <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for contrast nephropathy <|end|>\n<|assistant|> \n(atorvastatin | Lipitor) & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in elderly surgical patients <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & elderly & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and hyperglycemia in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & hyperglycemia & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAI-based cuff pressure optimization <|end|>\n<|assistant|> \n(AI | \"artificial intelligence\") & based & cuff & pressure & optimization <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT via upper vs lower GI for CDI <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (upper | lower) & (GI | route) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for anastomotic leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"anastomotic leaks\" | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSimplified PE diagnostic algorithms <|end|>\n<|assistant|> \nsimplified & (PE | \"pulmonary embolism\") & diagnostic & algorithms <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenotyping <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for ventilator-induced lung injury prevention <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (\"ventilator induced\" | VILI) & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in immune checkpoint inhibitor myocarditis <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"immune checkpoint\" | ICI) & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRomiplostim vs eltrombopag in HELLP <|end|>\n<|assistant|> \n(romiplostim | eltrombopag) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlockchain for INR data security <|end|>\n<|assistant|> \nblockchain & (INR | \"international ratio\") & (\"data security\" | privacy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total fetal lung volume in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total fetal lung volume\" | TFLV) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer surgery cost <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & cost <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSalivary CRP for IBD monitoring <|end|>\n<|assistant|> \n(salivary | saliva) & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUniversal vs genotype-guided DAPT <|end|>\n<|assistant|> \n(universal | genotype) & guided & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope in COVID ARDS <|end|>\n<|assistant|> \n(PCT | procalcitonin) & slope & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntegrated PTB risk assessment <|end|>\n<|assistant|> \nintegrated & (PTB | \"preterm birth\") & (risk | assessment) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine-propofol combination for AWS <|end|>\n<|assistant|> \n(ketamine | propofol) & combination & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo fusion imaging for pericardiocentesis <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & (fusion | imaging) & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for knee OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP in gallstone pancreatitis <|end|>\n<|assistant|> \n(same-session | immediate) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID-associated HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement accuracy <|end|>\n<|assistant|> \n(forearm | BP) & measurement & accuracy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVenous Doppler in IUGR management <|end|>\n<|assistant|> \n(venous | ductus) & Doppler & (IUGR | \"intrauterine growth restriction\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in dialysis patients <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (dialysis | ESRD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and mortality in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & mortality & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs placebo for HRS reversal <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & placebo & (HRS | \"hepatorenal syndrome\") & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla for ASD closure <|end|>\n<|assistant|> \n(Occlutech | Figulla) & device & (ASD | \"atrial septal defect\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire FilmArray for sepsis <|end|>\n<|assistant|> \n(BioFire | FilmArray) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV mapping in amyloidosis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & mapping & amyloidosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate index in septic shock resuscitation <|end|>\n<|assistant|> \n(lactate | index) & (\"septic shock\" | sepsis) & resuscitation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAgent-based modeling for CTS surgical access <|end|>\n<|assistant|> \n\"agent based modeling\" & (CTS | \"carpal tunnel syndrome\") & (surgical | access) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndividualized BP targets in ICH <|end|>\n<|assistant|> \nindividualized & (BP | \"blood pressure\") & targets & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrastive learning for lymphocyte classification <|end|>\n<|assistant|> \n\"contrastive learning\" & lymphocyte & classification <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFixed vs calculated radioiodine dose for Graves' <|end|>\n<|assistant|> \n(fixed | calculated) & (radioiodine | I-131) & dose & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for high-grade Ta NMIBC <|end|>\n<|assistant|> \n(Adstiladrin | nadofaragene) & (\"high grade\" | Ta) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after NSTEMI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (NSTEMI | \"non ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for Hurthle cell lesions <|end|>\n<|assistant|> \n(ThyGeNEXT | molecular) & (\"Hurthle cell\" | oncocytic) & lesions <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN prevention <|end|>\n<|assistant|> \n(high dose | statin) & atorvastatin & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for capsule endoscopy <|end|>\n<|assistant|> \n(meta-learning | transfer) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in patients with dementia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (dementia | cognitive) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone and neuromuscular blocking agents <|end|>\n<|assistant|> \n(hydrocortisone | corticosteroid) & (\"neuromuscular blocking agents\" | NMBAs) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPredictive cuff pressure algorithms <|end|>\n<|assistant|> \npredictive & cuff & pressure & algorithms <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT donor selection for CDI <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (donor | selection) & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic suturing vs clips for GI defects <|end|>\n<|assistant|> \nendoscopic & (suturing | clips) & (\"GI defects\" | perforation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAge-adjusted D-dimer cutoffs <|end|>\n<|assistant|> \n(age-adjusted | modified) & (D-dimer | \"fibrin fragment\") & cutoffs <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiac surgery <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"cardiac surgery\" | congenital) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subtyping <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subtyping <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for prone positioning in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (prone | positioning) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Chagas cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (Chagas | cardiomyopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs romiplostim in HELLP <|end|>\n<|assistant|> \n(avatrombopag | romiplostim) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIoT-based INR monitoring <|end|>\n<|assistant|> \n(IoT | \"internet of things\") & (INR | \"international ratio\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI liver-to-thoracic ratio in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"liver to thoracic ratio\" | LTR) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic vs laparoscopic rectal cancer surgery <|end|>\n<|assistant|> \n(robotic | laparoscopic) & (\"rectal cancer\" | rectum) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStool calprotectin vs CRP in IBD <|end|>\n<|assistant|> \n(stool | fecal) & calprotectin & (CRP | \"C-reactive protein\") & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-effectiveness of genotype-guided DAPT <|end|>\n<|assistant|> \n(cost-effectiveness | economic) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life in septic shock <|end|>\n<|assistant|> \n(PCT | procalcitonin) & (\"half life\" | kinetics) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-modal PTB prediction <|end|>\n<|assistant|> \n(multi-modal | combined) & (PTB | \"preterm birth\") & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine adjunct for benzodiazepine-resistant AWS <|end|>\n<|assistant|> \nketamine & adjunct & (benzodiazepine-resistant | refractory) & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed guides for pericardiocentesis <|end|>\n<|assistant|> \n(3D | printed) & guides & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for knee OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (knee | OA | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nERCP vs surgery for gallstone pancreatitis with choledocholithiasis <|end|>\n<|assistant|> \n(ERCP | \"endoscopic retrograde cholangiopancreatography\") & surgery & (gallstone | biliary) & pancreatitis & choledocholithiasis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in lymphoma-associated HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (lymphoma | associated) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWrist BP measurement validation <|end|>\n<|assistant|> \n(wrist | BP) & measurement & validation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus a-wave in IUGR <|end|>\n<|assistant|> \n(ductus venosus | DV) & (a-wave | reversal) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in chronic kidney disease <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (CKD | \"chronic kidney disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and infection risk in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (infection | risk) & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin albumin vs albumin alone for HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & albumin & (albumin | alone) & (HRS | \"hepatorenal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD occluder <|end|>\n<|assistant|> \n(Gore | Cardioform) & device & (ASD | \"atrial septal defect\") & occluder <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for neonatal sepsis <|end|>\n<|assistant|> \n(SeptiFast | PCR) & neonatal & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in Anderson-Fabry disease <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (\"Anderson-Fabry\" | Fabry) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis stratification <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & sepsis & stratification <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS surgical wait times <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"wait times\" | access) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndividualized systolic BP in ICH <|end|>\n<|assistant|> \nindividualized & (systolic | BP) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised contrastive learning for lymphocytes <|end|>\n<|assistant|> \n\"self supervised\" & \"contrastive learning\" & lymphocytes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD vs fixed dose for Graves' radioiodine <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & (fixed | dose) & (\"Graves'' disease\" | radioiodine) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder cancer <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder cancer\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after CABG <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (CABG | \"coronary artery bypass\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate thyroid cytology <|end|>\n<|assistant|> \n(ThyraMIR | miRNA) & (indeterminate | Bethesda) & thyroid & cytology <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap donor site <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (\"donor site\" | abdomen) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention <|end|>\n<|assistant|> \n(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (apixaban | Eliquis) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTransformer networks for capsule endoscopy <|end|>\n<|assistant|> \n(transformer | networks) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in obstructive sleep apnea <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (OSA | \"obstructive sleep apnea\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and secondary infections in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (\"secondary infections\" | infection) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMachine learning for cuff pressure prediction <|end|>\n<|assistant|> \n\"machine learning\" & cuff & pressure & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUniversal donor FMT for CDI <|end|>\n<|assistant|> \n(universal | donor) & (FMT | \"fecal microbiota transplantation\") & (CDI | \"C. diff infection\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for esophageal leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"esophageal leaks\" | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for clinical probability <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (\"clinical probability\" | Wells) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neonates with septic shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & neonates & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI treatment response <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & (treatment | response) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT-guided PEEP in COVID ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & guided & (PEEP | \"positive end-expiratory pressure\") & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in peripartum cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"peripartum cardiomyopathy\" | PPCM) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag vs avatrombopag in HELLP <|end|>\n<|assistant|> \n(lusutrombopag | avatrombopag) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSmartphone-based INR monitoring <|end|>\n<|assistant|> \n(smartphone | mobile) & (INR | \"international ratio\") & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected TFLV in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected total fetal lung volume\" | O/E TFLV) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost analysis robotic vs laparoscopic rectal surgery <|end|>\n<|assistant|> \ncost & analysis & robotic & laparoscopic & (rectal | surgery) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin thresholds in UC remission <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & thresholds & (UC | \"ulcerative colitis\") & remission <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nReal-world cost-effectiveness of genotype-guided DAPT <|end|>\n<|assistant|> \n(real-world | effectiveness) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") & (cost | economic) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics as antibiotic stewardship tool <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (antibiotic | stewardship) & tool <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omic PTB prediction <|end|>\n<|assistant|> \n(multi-omic | integrated) & (PTB | \"preterm birth\") & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine infusion protocols for AWS <|end|>\n<|assistant|> \nketamine & infusion & protocols & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPatient-specific 3D models for pericardiocentesis <|end|>\n<|assistant|> \n(patient-specific | 3D) & models & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for hand OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (hand | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly ERCP in predicted severe gallstone pancreatitis <|end|>\n<|assistant|> \nearly & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & predicted & severe & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nModified HScore for COVID-associated HLH <|end|>\n<|assistant|> \n(modified | adapted) & (HScore | \"HScore\") & (COVID | SARS-CoV-2) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference and cuff size <|end|>\n<|assistant|> \n(upper arm | circumference) & cuff & size <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical vein Doppler in IUGR <|end|>\n<|assistant|> \n(umbilical | UV) & vein & Doppler & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in heart transplant recipients <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"heart transplant\" | transplantation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and COVID outcomes in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (COVID | SARS-CoV-2) & outcomes & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs noradrenaline for HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (noradrenaline | norepinephrine) & (HRS | \"hepatorenal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer Septal Occluder for secundum ASD <|end|>\n<|assistant|> \n(Amplatzer | occluder) & device & (\"secundum ASD\" | ASD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Candida for candidemia <|end|>\n<|assistant|> \n(T2Candida | panel) & candidemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloidosis subtypes <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (\"cardiac amyloidosis\" | AL | ATTR) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate in septic shock <|end|>\n<|assistant|> \n(lactate | clearance) & rate & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS post-operative care <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"post operative\" | recovery) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP variability and ICH outcome <|end|>\n<|assistant|> \n(systolic | BP) & variability & (ICH | \"intracerebral hemorrhage\") & outcome <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for COVID lymphocytes <|end|>\n<|assistant|> \n\"self supervised learning\" & (COVID | SARS-CoV-2) & lymphocytes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD vs standard dose for toxic nodular goiter <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & (standard | dose) & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVicineum for NMIBC <|end|>\n<|assistant|> \n(vicineum | oportuzumab) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after coronary bypass <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"coronary bypass\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for Hurthle cell nodules <|end|>\n<|assistant|> \n(Afirma | GSC) & (\"Hurthle cell\" | oncocytic) & nodules <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in transverse rectus abdominis flap <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TRAM | \"transverse rectus abdominis\") & flap <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose rosuvastatin for CIN prevention <|end|>\n<|assistant|> \n(high dose | statin) & rosuvastatin & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for rivaroxaban reversal <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (rivaroxaban | Xarelto) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in patients with Alzheimer's <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"Alzheimer''s disease\" | dementia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHydrocortisone and insulin resistance in sepsis <|end|>\n<|assistant|> \n(hydrocortisone | corticosteroid) & (\"insulin resistance\" | hyperglycemia) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nReinforcement learning for cuff pressure control <|end|>\n<|assistant|> \n\"reinforcement learning\" & cuff & pressure & control <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFrozen vs fresh FMT efficacy <|end|>\n<|assistant|> \n(frozen | fresh) & (FMT | \"fecal microbiota transplantation\") & efficacy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOverStitch for GI fistula closure <|end|>\n<|assistant|> \n(OverStitch | endoscopic) & (fistula | closure) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWells score vs YEARS for PE <|end|>\n<|assistant|> \n(Wells | YEARS) & score & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric burn shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"burn shock\" | burns) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotyping <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotyping <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in severe ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & severe & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in chemotherapy-induced cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (chemotherapy | induced) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag dosing in HELLP <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & dosing & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBlockchain-secured INR data <|end|>\n<|assistant|> \nblockchain & secured & (INR | \"international ratio\") & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI quantitative lung volume in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (quantitative | volume) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal surgery learning curve <|end|>\n<|assistant|> \nrobotic & (rectal | surgery) & (\"learning curve\" | training) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC endoscopic remission <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (endoscopic | remission) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCost-utility of genotype-guided DAPT <|end|>\n<|assistant|> \n(cost-utility | economic) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin slope as predictor of VAP <|end|>\n<|assistant|> \n(PCT | procalcitonin) & slope & predictor & (VAP | \"ventilator associated pneumonia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIntegrated multi-omics for PTB <|end|>\n<|assistant|> \nintegrated & (multi-omics | biomarkers) & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine-benzodiazepine combination for AWS <|end|>\n<|assistant|> \n(ketamine | benzodiazepine) & combination & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo navigation for pericardiocentesis <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | echocardiography) & navigation & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for hand OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (hand | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after mild gallstone pancreatitis <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & mild & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in adult-onset Still's disease <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (\"adult onset Still''s disease\" | AOSD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nForearm BP in obesity <|end|>\n<|assistant|> \n(forearm | BP) & measurement & obesity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery Doppler pulsatility index in IUGR <|end|>\n<|assistant|> \n(umbilical | UA) & Doppler & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial hypercholesterolemia children <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (FH | \"familial hypercholesterolemia\") & children <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and pneumonia in elderly <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & pneumonia & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs octreotide for variceal bleeding <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (octreotide | Sandostatin) & (\"variceal bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore device for PFO closure <|end|>\n<|assistant|> \n(Gore | device) & (PFO | \"patent foramen ovale\") & closure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire Blood Culture Identification 2 <|end|>\n<|assistant|> \n(BioFire | BCID2) & (\"blood culture\" | identification) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in hypertrophic cardiomyopathy <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (HCM | \"hypertrophic cardiomyopathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in sepsis mortality prediction <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & sepsis & mortality & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS rehabilitation pathways <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (rehabilitation | pathways) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTime-dependent BP control in ICH <|end|>\n<|assistant|> \n(time-dependent | temporal) & (BP | \"blood pressure\") & control & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrastive self-supervised learning for lymphocytes <|end|>\n<|assistant|> \n\"contrastive self supervised learning\" & lymphocytes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCalculated radioiodine dose for toxic MNG <|end|>\n<|assistant|> \n(calculated | MIRD) & (radioiodine | I-131) & dose & (\"toxic MNG\" | multinodular) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS <|end|>\n<|assistant|> \n(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after acute MI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"acute MI\" | AMI) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for follicular lesions <|end|>\n<|assistant|> \n(ThyGeNEXT | molecular) & (follicular | lesions) & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in abdominal surgery <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & abdominal & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nStatin loading for CIN prevention <|end|>\n<|assistant|> \n(statin | loading) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal in ICH <|end|>\n<|assistant|> \n(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for capsule endoscopy few-shot <|end|>\n<|assistant|> \n(meta-learning | few-shot) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS monitoring in Down syndrome <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & monitoring & (\"Down syndrome\" | trisomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and glycemic variability in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (glycemic | glucose) & variability & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDeep reinforcement learning for cuff pressure <|end|>\n<|assistant|> \n\"deep reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiota engraftment <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiota | engraftment) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for duodenal leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (duodenal | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSimplified YEARS algorithm <|end|>\n<|assistant|> \nsimplified & (YEARS | algorithm) & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock dose <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & dose <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for persistent AKI <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (persistent | AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in pediatric ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & pediatric & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in diabetic cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (diabetic | diabetes) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag dosing in HELLP <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & dosing & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSecure multiparty computation for INR data <|end|>\n<|assistant|> \n(secure | multiparty) & computation & (INR | \"international ratio\") & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume percentiles in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | percentiles) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal surgery outcomes <|end|>\n<|assistant|> \nrobotic & (rectal | surgery) & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for predicting UC relapse <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & predicting & (UC | \"ulcerative colitis\") & relapse <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBudget impact of genotype-guided DAPT <|end|>\n<|assistant|> \n(budget | impact) & (genotype | CYP2C19) & guided & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin velocity in ventilator-associated pneumonia <|end|>\n<|assistant|> \n(PCT | procalcitonin) & velocity & (\"ventilator associated\" | VAP) & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-modal PTB risk model <|end|>\n<|assistant|> \n(multi-modal | integrated) & (PTB | \"preterm birth\") & (risk | model) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in cirrhosis <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & cirrhosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed personalized pericardiocentesis guides <|end|>\n<|assistant|> \n(3D | printed) & personalized & pericardiocentesis & guides <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for thumb CMC OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (\"thumb CMC\" | basal) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndex cholecystectomy timing after gallstone pancreatitis <|end|>\n<|assistant|> \n(index | interval) & cholecystectomy & timing & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic juvenile idiopathic arthritis <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (sJIA | \"systemic juvenile idiopathic arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWrist BP measurement in large arms <|end|>\n<|assistant|> \n(wrist | BP) & measurement & (large | arms) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebral-placental ratio in IUGR prediction <|end|>\n<|assistant|> \n(cerebral-placental | CPR) & ratio & (IUGR | \"intrauterine growth restriction\") & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in nephrotic syndrome <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"nephrotic syndrome\" | proteinuria) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS outcomes <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute variceal bleeding <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & acute & (\"variceal bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech ASD device <|end|>\n<|assistant|> \n(Occlutech | device) & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for pediatric sepsis <|end|>\n<|assistant|> \n(SeptiFast | PCR) & pediatric & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in iron overload cardiomyopathy <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (\"iron overload\" | hemochromatosis) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio vs SOFA in sepsis <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & (SOFA | \"sequential organ failure assessment\") & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS economic burden <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (economic | burden) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP trajectories in ICH recovery <|end|>\n<|assistant|> \n(systolic | BP) & trajectories & (ICH | \"intracerebral hemorrhage\") & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for COVID lymphocyte morphology <|end|>\n<|assistant|> \n\"self supervised learning\" & (COVID | SARS-CoV-2) & lymphocyte & morphology <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD dose calculation for toxic nodular goiter <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & dose & calculation & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(Adstiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate FNA <|end|>\n<|assistant|> \n(ThyraMIR | miRNA) & (indeterminate | FNA | \"fine needle aspiration\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin loading for CIN prevention <|end|>\n<|assistant|> \n(rosuvastatin | loading) & prevention & (CIN | \"contrast-induced nephropathy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for betrixaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (betrixaban | Bevyxxa) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for small bowel capsule endoscopy <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (\"small bowel\" | SB) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in Parkinson's disease <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"Parkinson''s disease\" | PD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial function in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (mitochondrial | function) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFederated learning for cuff pressure prediction <|end|>\n<|assistant|> \n\"federated learning\" & cuff & pressure & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDonor screening for FMT <|end|>\n<|assistant|> \n(donor | screening) & (FMT | \"fecal microbiota transplantation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for pancreatic leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (pancreatic | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for age and comorbidity <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (age | comorbidity) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in extracorporeal membrane oxygenation <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (ECMO | \"extracorporeal membrane oxygenation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenogroups <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenogroups <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP individualization in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & individualization & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in iron deficiency heart failure <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"iron deficiency\" | ID) & (\"heart failure\" | HF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP with thrombocytopenia <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR data <|end|>\n<|assistant|> \n(homomorphic | encryption) & (INR | \"international ratio\") & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected lung volume in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected lung volume\" | O/E LV) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer functional outcomes <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (functional | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment de-escalation <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"de escalation\" | reduction) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nReal-world CYP2C19 testing in DAPT <|end|>\n<|assistant|> \n(real-world | effectiveness) & (CYP2C19 | genotype) & testing & (DAPT | \"dual antiplatelet therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic duration <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & antibiotics & duration <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omic integration for PTB <|end|>\n<|assistant|> \n(multi-omic | integration) & (PTB | \"preterm birth\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine protocols for AWS in ICU <|end|>\n<|assistant|> \nketamine & protocols & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo-CT fusion for complex effusions <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | CT) & fusion & (complex | effusions) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for first CMC OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (\"first CMC\" | thumb) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP cholecystectomy <|end|>\n<|assistant|> \n(same-session | combined) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & cholecystectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in macrophage activation syndrome <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (MAS | \"macrophage activation syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff size <|end|>\n<|assistant|> \n(upper arm | circumference) & based & cuff & size <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery PI in IUGR <|end|>\n<|assistant|> \n(MCA | \"middle cerebral artery\") & (PI | \"pulsatility index\") & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in liver transplant recipients <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"liver transplant\" | transplantation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sepsis mortality <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & sepsis & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs vasopressin for varices <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (varices | variceal) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder <|end|>\n<|assistant|> \n(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid diagnosis <|end|>\n<|assistant|> \n(T2Bacteria | panel) & rapid & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac involvement of systemic diseases <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (cardiac | heart) & (systemic | diseases) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time prognostic value <|end|>\n<|assistant|> \n(lactate | clearance) & time & (prognostic | value) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS return-to-work <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"return to work\" | recovery) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and hematoma growth in ICH <|end|>\n<|assistant|> \n(systolic | BP) & (\"hematoma growth\" | expansion) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte subpopulations <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & subpopulations <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose calculation methods <|end|>\n<|assistant|> \n(radioiodine | I-131) & dose & calculation & methods <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for high-risk NMIBC <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (\"high risk\" | high-risk) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after drug-eluting stent <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (DES | \"drug-eluting stent\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression for thyroid cancer <|end|>\n<|assistant|> \n(Afirma | Xpression) & (\"thyroid cancer\" | malignancy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in transverse abdominis plane block <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in CKD <|end|>\n<|assistant|> \n(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for apixaban reversal <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (apixaban | Eliquis) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for ulcer detection <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & ulcer & detection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in multiple sclerosis <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (MS | \"multiple sclerosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and immune paralysis in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (\"immune paralysis\" | immunosuppression) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nReinforcement learning for cuff pressure optimization <|end|>\n<|assistant|> \n\"reinforcement learning\" & cuff & pressure & optimization <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT donor-recipient matching <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (donor | recipient) & matching <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for colorectal leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (colorectal | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWells score in elderly for PE <|end|>\n<|assistant|> \n(Wells | score) & elderly & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in congenital heart disease surgery <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (\"congenital heart disease\" | CHD) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotypes in sepsis <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for personalized ventilation in ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & personalized & ventilation & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in tachycardia-induced cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"tachycardia induced\" | cardiomyopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs placebo in HELLP <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & placebo & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR privacy <|end|>\n<|assistant|> \n(zero-knowledge | proofs) & (INR | \"international ratio\") & privacy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung area quantification in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung area\" | quantification) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer quality of life <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (QoL | \"quality of life\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD postoperative recurrence <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (postoperative | recurrence) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing implementation challenges <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (implementation | challenges) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life as antibiotic guide <|end|>\n<|assistant|> \n(PCT | procalcitonin) & (\"half life\" | kinetics) & (antibiotic | guide) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction model <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & (prediction | model) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in liver disease <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"liver disease\" | cirrhosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis models <|end|>\n<|assistant|> \n(3D | printed) & patient-specific & pericardiocentesis & models <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for thumb OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (thumb | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after endoscopic stone removal <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & endoscopic & (stone | removal) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in secondary HLH validation <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (secondary | HLH) & validation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement protocols <|end|>\n<|assistant|> \n(forearm | BP) & measurement & protocols <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery resistance index in IUGR <|end|>\n<|assistant|> \n(umbilical | UA) & (RI | \"resistance index\") & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in HIV-associated dyslipidemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (HIV | \"human immunodeficiency virus\") & dyslipidemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator weaning <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ventilator | weaning) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin in acute kidney injury with cirrhosis <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (AKI | \"acute kidney injury\") & cirrhosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform for secundum ASD <|end|>\n<|assistant|> \n(Gore | Cardioform) & device & (\"secundum ASD\" | ASD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire for pediatric bloodstream infections <|end|>\n<|assistant|> \n(BioFire | FilmArray) & pediatric & (\"bloodstream infections\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in chemotherapy cardiotoxicity <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (chemotherapy | cardiotoxicity) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for sepsis severity <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & sepsis & severity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS cost-effectiveness <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (cost-effectiveness | economic) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUltra-early systolic BP reduction in ICH <|end|>\n<|assistant|> \n(ultra-early | immediate) & (systolic | BP) & reduction & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nContrastive self-supervised learning for lymphocyte subtypes <|end|>\n<|assistant|> \n\"contrastive self supervised learning\" & lymphocyte & subtypes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nActivity-based radioiodine dosing for Graves' disease <|end|>\n<|assistant|> \n(activity | MIRD) & based & (radioiodine | I-131) & dosing & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for BCG-unresponsive CIS <|end|>\n<|assistant|> \n(Instiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (CIS | \"carcinoma in situ\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after myocardial infarction <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (MI | \"myocardial infarction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for Hurthle cell lesions <|end|>\n<|assistant|> \n(RosettaGX | Reveal) & (\"Hurthle cell\" | oncocytic) & lesions <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast surgery <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & breast & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN in diabetes <|end|>\n<|assistant|> \n(high dose | statin) & atorvastatin & (CIN | \"contrast-induced nephropathy\") & diabetes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for ICH on dabigatran <|end|>\n<|assistant|> \n(idarucizumab | Praxbind) & (ICH | \"intracerebral hemorrhage\") & (dabigatran | Pradaxa) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy lesions <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy & lesions <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in epilepsy surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (epilepsy | seizures) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and microbiome in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (microbiome | microbiota) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDeep RL for cuff pressure regulation <|end|>\n<|assistant|> \n\"deep reinforcement learning\" & cuff & pressure & regulation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT engraftment biomarkers <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & engraftment & biomarkers <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-surgical leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post surgical\" | post-operative) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSimplified Wells score for PE <|end|>\n<|assistant|> \nsimplified & (Wells | score) & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in neonates on ECMO <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & neonates & (ECMO | \"extracorporeal membrane oxygenation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenotyping in COVID <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in obesity ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & obesity & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Takotsubo cardiomyopathy <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (Takotsubo | cardiomyopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag in HELLP syndrome <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSecure enclaves for INR data <|end|>\n<|assistant|> \n(secure | enclaves) & (INR | \"international ratio\") & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume Z-scores in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | Z-scores) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer sexual function <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"sexual function\" | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for predicting CD surgery <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & predicting & (CD | \"Crohn''s disease\") & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in clinical practice <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (\"clinical practice\" | implementation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for VAP diagnosis <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (VAP | \"ventilator associated pneumonia\") & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB risk stratification <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & (risk | stratification) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for refractory AWS in ICU <|end|>\n<|assistant|> \nketamine & (refractory | resistant) & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed models for complex pericardiocentesis <|end|>\n<|assistant|> \n(3D | printed) & models & complex & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive hand OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (erosive | hand) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after endoscopic sphincterotomy <|end|>\n<|assistant|> \nearly & cholecystectomy & (\"endoscopic sphincterotomy\" | ERCP) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID cytokine storm <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (\"cytokine storm\" | hyperinflammation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThigh cuff BP measurement <|end|>\n<|assistant|> \n(thigh | cuff) & (BP | \"blood pressure\") & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in late IUGR management <|end|>\n<|assistant|> \n(cerebroplacental | CPR) & ratio & (late | onset) & (IUGR | \"intrauterine growth restriction\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in post-kidney transplant <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"kidney transplant\" | transplantation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ICU outcomes <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ICU | \"intensive care\") & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for type 1 HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (\"type 1\" | HRS) & (\"hepatorenal syndrome\" | HRS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech PFO device <|end|>\n<|assistant|> \n(Occlutech | device) & (PFO | \"patent foramen ovale\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for pediatric bacteremia <|end|>\n<|assistant|> \n(Accelerate | Pheno) & pediatric & bacteremia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloid vs hypertrophic cardiomyopathy <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (amyloid | HCM) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate prognostic accuracy <|end|>\n<|assistant|> \n(lactate | clearance) & rate & (prognostic | accuracy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS disability prevention <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (disability | prevention) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and functional outcome in ICH <|end|>\n<|assistant|> \n(systolic | BP) & (functional | outcome) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte dynamics <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & dynamics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD vs fixed dose for toxic adenoma <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & (fixed | dose) & (\"toxic adenoma\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder CIS <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder CIS\" | carcinoma) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after stent thrombosis <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"stent thrombosis\" | thrombosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for indeterminate thyroid lesions <|end|>\n<|assistant|> \n(ThyraMIR | miRNA) & (indeterminate | Bethesda) & thyroid & lesions <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap reconstruction pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN in renal impairment <|end|>\n<|assistant|> \n(rosuvastatin | Crestor) & (CIN | \"contrast-induced nephropathy\") & (\"renal impairment\" | CKD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for bleeding detection in capsule endoscopy <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (bleeding | hemorrhage) & detection & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in traumatic brain injury <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (TBI | \"traumatic brain injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and endothelial dysfunction in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (endothelial | dysfunction) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFederated reinforcement learning for cuff pressure <|end|>\n<|assistant|> \n\"federated reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT donor microbiome composition <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (donor | microbiome) & composition <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for bariatric surgery leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (bariatric | surgery) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for renal function <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (\"renal function\" | CKD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiac arrest <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"cardiac arrest\" | resuscitation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenogroups in sepsis <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenogroups & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral pneumonia <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (unilateral | pneumonia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in perimyocarditis <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & perimyocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP thrombocytopenia <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDifferential privacy for INR data <|end|>\n<|assistant|> \n(differential | privacy) & (INR | \"international ratio\") & data <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-thorax ratio in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to thorax ratio\" | LTR) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer bowel function <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"bowel function\" | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC de-escalation criteria <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"de escalation\" | criteria) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBarriers to CYP2C19 testing <|end|>\n<|assistant|> \nbarriers & (CYP2C19 | genotype) & testing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic stewardship <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (antibiotic | stewardship) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in high-risk women <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"high risk\" | high-risk) & women <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine vs benzodiazepines for AWS in ICU <|end|>\n<|assistant|> \nketamine & (benzodiazepines | BZD) & (AWS | \"alcohol withdrawal syndrome\") & (ICU | \"intensive care\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D echo-CT fusion for complex pericardial effusions <|end|>\n<|assistant|> \n(3D | \"three dimensional\") & (echo | CT) & fusion & (complex | \"pericardial effusions\" | fluid) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for first carpometacarpal OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (\"first carpometacarpal\" | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-session ERCP and laparoscopic cholecystectomy <|end|>\n<|assistant|> \n(same-session | combined) & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & laparoscopic & cholecystectomy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic lupus erythematosus <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (SLE | \"systemic lupus erythematosus\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference and BP accuracy <|end|>\n<|assistant|> \n(upper arm | circumference) & (BP | \"blood pressure\") & accuracy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in early IUGR <|end|>\n<|assistant|> \n(MCA | \"middle cerebral artery\") & Doppler & (early | onset) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in familial combined hyperlipidemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"familial combined hyperlipidemia\" | FCHL) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS prevention <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & prevention <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for hepatorenal syndrome acute kidney injury <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (HRS | \"hepatorenal syndrome\") & (AKI | \"acute kidney injury\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder long-term outcomes <|end|>\n<|assistant|> \n(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & \"long term\" & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Candida for neonatal candidiasis <|end|>\n<|assistant|> \n(T2Candida | panel) & neonatal & candidiasis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac sarcoid vs giant cell myocarditis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (\"cardiac sarcoid\" | \"giant cell myocarditis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in septic shock mortality <|end|>\n<|assistant|> \n(lactate | clearance) & time & (\"septic shock\" | sepsis) & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS prevention programs <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (prevention | programs) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction speed in ICH <|end|>\n<|assistant|> \n(systolic | BP) & reduction & speed & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte kinetics <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & kinetics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPersonalized radioiodine for toxic MNG <|end|>\n<|assistant|> \npersonalized & (radioiodine | I-131) & (\"toxic MNG\" | multinodular) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVicinium for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(vicinium | oportuzumab) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after left main PCI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"left main\" | LM) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for medullary thyroid cancer <|end|>\n<|assistant|> \n(Afirma | GSC) & (\"medullary thyroid cancer\" | MTC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap donor site <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\") & (\"donor site\" | abdomen) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in STEMI <|end|>\n<|assistant|> \n(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (STEMI | \"ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal in ICH <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for rare capsule endoscopy findings <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy & (rare | findings) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in Huntington's disease <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"Huntington''s disease\" | chorea) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and coagulation in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & coagulation & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMeta-reinforcement learning for cuff pressure <|end|>\n<|assistant|> \n\"meta reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT donor microbiome diversity <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (donor | microbiome) & diversity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for pancreaticoduodenectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (pancreaticoduodenectomy | Whipple) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for D-dimer assay <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & assay <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with ARDS <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI endotypes <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID pneumonia <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in Chagas heart disease <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (Chagas | cardiomyopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP platelet recovery <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (platelet | recovery) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPrivacy-preserving INR analytics <|end|>\n<|assistant|> \n(privacy | preserving) & (INR | \"international ratio\") & analytics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI total lung volume prediction in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"total lung volume\" | TLV) & prediction & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer urinary function <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"urinary function\" | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD de-escalation <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"de escalation\" | reduction) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing cost-effectiveness analysis <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (cost-effectiveness | economic) & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin half-life for VAP management <|end|>\n<|assistant|> \n(PCT | procalcitonin) & (\"half life\" | kinetics) & (VAP | \"ventilator associated pneumonia\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in twins <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & twins <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS with hepatic encephalopathy <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"hepatic encephalopathy\" | cirrhosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis training <|end|>\n<|assistant|> \n(3D | printed) & patient-specific & pericardiocentesis & training <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for base of thumb OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (\"base of thumb\" | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for choledocholithiasis <|end|>\n<|assistant|> \nearly & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & choledocholithiasis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in Kawasaki disease <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (\"Kawasaki disease\" | vasculitis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nForearm BP in large arms <|end|>\n<|assistant|> \n(forearm | BP) & measurement & (large | arms) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical vein pulsations in IUGR <|end|>\n<|assistant|> \n(umbilical | UV) & vein & pulsations & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in metabolic syndrome <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"metabolic syndrome\" | insulin) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator duration <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ventilator | duration) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute-on-chronic liver failure <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (\"acute on chronic\" | ACLF) & (\"liver failure\" | cirrhosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform PFO occluder <|end|>\n<|assistant|> \n(Gore | Cardioform) & (PFO | \"patent foramen ovale\") & occluder <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire BCID2 for neonatal sepsis <|end|>\n<|assistant|> \n(BioFire | BCID2) & neonatal & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac involvement of rheumatoid arthritis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (cardiac | heart) & (RA | \"rheumatoid arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for mortality prediction <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & mortality & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS occupational health <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (occupational | health) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP trajectory in ICH recovery <|end|>\n<|assistant|> \n(systolic | BP) & trajectory & (ICH | \"intracerebral hemorrhage\") & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte activation <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & activation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose optimization for toxic adenoma <|end|>\n<|assistant|> \n(radioiodine | I-131) & dose & optimization & (\"toxic adenoma\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for high-grade Ta urothelial carcinoma <|end|>\n<|assistant|> \n(Adstiladrin | nadofaragene) & (\"high grade\" | Ta) & (\"urothelial carcinoma\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after ACS <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyGeNEXT for papillary thyroid carcinoma <|end|>\n<|assistant|> \n(ThyGeNEXT | molecular) & (\"papillary thyroid\" | PTC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose rosuvastatin for CIN in STEMI <|end|>\n<|assistant|> \n(high dose | statin) & rosuvastatin & (CIN | \"contrast-induced nephropathy\") & (STEMI | \"ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (apixaban | Eliquis) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for polyp detection in capsule endoscopy <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (polyp | detection) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in schizophrenia <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & schizophrenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and gut barrier in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (\"gut barrier\" | permeability) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-agent RL for cuff pressure <|end|>\n<|assistant|> \n\"multi agent reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome predictors of response <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | predictors) & response <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for gastrectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (gastrectomy | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWells score in pregnancy for PE <|end|>\n<|assistant|> \n(Wells | score) & pregnancy & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric congenital diaphragmatic hernia <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI endotypes in COVID <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in obese COVID ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & obese & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute myocarditis <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & acute & myocarditis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag for HELLP thrombocytopenia <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSecure multi-party computation for INR <|end|>\n<|assistant|> \n(secure | multi-party) & computation & (INR | \"international ratio\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected lung volume percentiles <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected lung volume\" | O/E LV) & percentiles <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer long-term function <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & \"long term\" & function <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment optimization <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (treatment | optimization) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in community hospitals <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (community | hospitals) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for sepsis subphenotyping <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & sepsis & subphenotyping <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in nulliparous women <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & nulliparous <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine protocols for severe AWS <|end|>\n<|assistant|> \nketamine & protocols & severe & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed phantoms for pericardiocentesis training <|end|>\n<|assistant|> \n(3D | printed) & phantoms & pericardiocentesis & training <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive hand osteoarthritis <|end|>\n<|assistant|> \n(autologous | protein) & solution & erosive & (hand | osteoarthritis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for gallstone pancreatitis <|end|>\n<|assistant|> \nearly & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (gallstone | biliary) & pancreatitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in adult-onset Still's disease validation <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (\"adult onset Still''s disease\" | AOSD) & validation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWrist BP in clinical practice <|end|>\n<|assistant|> \n(wrist | BP) & measurement & (\"clinical practice\" | accuracy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus a-wave reversal in IUGR <|end|>\n<|assistant|> \n(ductus venosus | DV) & (\"a-wave reversal\" | reversal) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in homozygous familial hypercholesterolemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (homozygous | HoFH) & (FH | \"familial hypercholesterolemia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and sepsis incidence <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & sepsis & incidence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs norepinephrine for septic shock in cirrhosis <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (norepinephrine | noradrenaline) & (\"septic shock\" | sepsis) & cirrhosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla Flex II ASD <|end|>\n<|assistant|> \n(Occlutech | Figulla) & device & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for sepsis in immunocompromised <|end|>\n<|assistant|> \n(SeptiFast | PCR) & sepsis & immunocompromised <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac transthyretin amyloidosis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (cardiac | ATTR) & amyloidosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance rate in septic shock resuscitation <|end|>\n<|assistant|> \n(lactate | clearance) & rate & (\"septic shock\" | sepsis) & resuscitation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS public health impact <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"public health\" | impact) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and cognitive outcome in ICH <|end|>\n<|assistant|> \n(systolic | BP) & (cognitive | outcome) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte senescence <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & senescence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD dose for toxic MNG <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & dose & (\"toxic MNG\" | multinodular) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for carcinoma in situ <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (\"carcinoma in situ\" | CIS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after stroke <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & stroke <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for medullary thyroid cancer <|end|>\n<|assistant|> \n(ThyraMIR | miRNA) & (\"medullary thyroid\" | MTC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap pain control <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (pain | control) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in CKD <|end|>\n<|assistant|> \n(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for rivaroxaban reversal <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (rivaroxaban | Xarelto) & reversal <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for small bowel inflammation <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (\"small bowel\" | SB) & inflammation & (capsule | VCE) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in traumatic brain injury surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (TBI | \"traumatic brain injury\") & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial biogenesis in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (mitochondrial | biogenesis) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHierarchical RL for cuff pressure <|end|>\n<|assistant|> \n\"hierarchical reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome signatures <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | signatures) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for sleeve gastrectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"sleeve gastrectomy\" | bariatric) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for cancer <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & cancer <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric ARDS <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotypes in burn patients <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & burns <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP in unilateral lung injury <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & (unilateral | lung) & injury <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute decompensated heart failure <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag dosing in pregnancy <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & dosing & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR verification <|end|>\n<|assistant|> \n(zero-knowledge | proofs) & (INR | \"international ratio\") & verification <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung-to-head ratio z-scores in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung to head ratio\" | LHR) & (Z-scores | standardized) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer recurrence <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & recurrence <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD treatment intensification <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (intensification | escalation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in elderly patients <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & elderly <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics as sepsis biomarker <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & sepsis & biomarker <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in IVF pregnancies <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (IVF | \"in vitro fertilization\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS with delirium tremens <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"delirium tremens\" | DT) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis <|end|>\n<|assistant|> \n(3D | printed) & simulators & pericardiocentesis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for carpometacarpal OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (carpometacarpal | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after ERCP for cholangitis <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & cholangitis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID-associated hyperinflammation <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (COVID | SARS-CoV-2) & hyperinflammation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based BP measurement <|end|>\n<|assistant|> \n(upper arm | circumference) & based & (BP | \"blood pressure\") & measurement <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery PI in early-onset IUGR <|end|>\n<|assistant|> \n(MCA | \"middle cerebral artery\") & (PI | \"pulsatility index\") & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in nephrotic syndrome dyslipidemia <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"nephrotic syndrome\" | proteinuria) & dyslipidemia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and septic shock mortality <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (\"septic shock\" | sepsis) & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs placebo for type 1 HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & placebo & (\"type 1\" | HRS) & (\"hepatorenal syndrome\" | HRS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder closure rates <|end|>\n<|assistant|> \n(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & (closure | rates) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Bacteria for rapid diagnosis in immunocompromised <|end|>\n<|assistant|> \n(T2Bacteria | panel) & rapid & diagnosis & immunocompromised <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac light-chain amyloidosis <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (cardiac | AL) & amyloidosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time in sepsis mortality prediction <|end|>\n<|assistant|> \n(lactate | clearance) & time & sepsis & mortality & prediction <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS work disability <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"work disability\" | absence) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction magnitude in ICH <|end|>\n<|assistant|> \n(systolic | BP) & reduction & magnitude & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte exhaustion <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & exhaustion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose personalization for toxic MNG <|end|>\n<|assistant|> \n(radioiodine | I-131) & dose & personalization & (\"toxic MNG\" | multinodular) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVicineum for BCG-unresponsive urothelial carcinoma <|end|>\n<|assistant|> \n(vicineum | oportuzumab) & (BCG-unresponsive | resistant) & (\"urothelial carcinoma\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after coronary artery bypass <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"coronary artery bypass\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for anaplastic thyroid cancer <|end|>\n<|assistant|> \n(Afirma | GSC) & (\"anaplastic thyroid\" | ATC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block for abdominal surgery <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block & abdominal & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention in NSTEMI <|end|>\n<|assistant|> \n(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & (NSTEMI | \"non ST elevation MI\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for apixaban reversal in gastrointestinal bleeding <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (apixaban | Eliquis) & reversal & (\"gastrointestinal bleeding\" | GIB) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy video analysis <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy & video & analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in brain tumor surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"brain tumor\" | glioma) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and autophagy in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & autophagy & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for cuff pressure RL <|end|>\n<|assistant|> \n(meta-learning | meta) & cuff & pressure & (RL | \"reinforcement learning\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT response prediction models <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & response & (prediction | models) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for esophagectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (esophagectomy | leak) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for inflammatory conditions <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (inflammatory | conditions) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with cardiomyopathy <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & cardiomyopathy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenotyping in burns <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & burns <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP optimization in chest trauma <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & optimization & (\"chest trauma\" | injury) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute heart failure <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"acute heart failure\" | AHF) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP syndrome platelet count <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (\"platelet count\" | PLT) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR computation <|end|>\n<|assistant|> \n(homomorphic | encryption) & (INR | \"international ratio\") & computation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume to body weight ratio in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume to body weight\" | ratio) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer survival <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & survival <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD postoperative monitoring <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (postoperative | monitoring) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in acute coronary syndrome <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for COVID-19 prognosis <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (COVID | SARS-CoV-2) & prognosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in multiple gestation <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"multiple gestation\" | twins) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in critical care <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"critical care\" | ICU) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis models <|end|>\n<|assistant|> \n(3D | printed) & patient-specific & pericardiocentesis & models <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive CMC OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & erosive & (CMC | carpometacarpal) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIndex cholecystectomy after endoscopic retrograde cholangiopancreatography <|end|>\n<|assistant|> \n(index | interval) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in systemic JIA <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (sJIA | \"systemic juvenile idiopathic arthritis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nForearm BP measurement in clinical guidelines <|end|>\n<|assistant|> \n(forearm | BP) & measurement & (guidelines | recommendations) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUmbilical artery absent end-diastolic flow in IUGR <|end|>\n<|assistant|> \n(umbilical | UA) & artery & (\"absent end diastolic flow\" | AEDF) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in chronic inflammatory diseases <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (chronic | inflammatory) & diseases <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ICU-acquired weakness <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (\"ICU acquired weakness\" | myopathy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs albumin for HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & albumin & (HRS | \"hepatorenal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD occluder <|end|>\n<|assistant|> \n(Gore | Cardioform) & device & (ASD | \"atrial septal defect\") & occluder <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire for sepsis in burns <|end|>\n<|assistant|> \n(BioFire | FilmArray) & sepsis & burns <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in radiation-induced cardiotoxicity <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (radiation | induced) & cardiotoxicity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio in septic shock resuscitation <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & (\"septic shock\" | sepsis) & resuscitation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS productivity loss <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"productivity loss\" | economic) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and hematoma volume in ICH <|end|>\n<|assistant|> \n(systolic | BP) & (\"hematoma volume\" | size) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte heterogeneity <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & heterogeneity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCalculated radioiodine dose for Graves' disease <|end|>\n<|assistant|> \n(calculated | MIRD) & (radioiodine | I-131) & dose & (\"Graves'' disease\" | hyperthyroidism) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAdstiladrin for BCG-unresponsive NMIBC <|end|>\n<|assistant|> \n(Adstiladrin | nadofaragene) & (BCG-unresponsive | resistant) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after DES <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (DES | \"drug-eluting stent\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for papillary carcinoma <|end|>\n<|assistant|> \n(RosettaGX | Reveal) & (\"papillary carcinoma\" | PTC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in breast reconstruction pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (\"breast reconstruction\" | mastectomy) & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose statin for CIN prevention meta-analysis <|end|>\n<|assistant|> \n(high dose | statin) & prevention & (CIN | \"contrast-induced nephropathy\") & meta-analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for dabigatran reversal in GI bleeding <|end|>\n<|assistant|> \n(idarucizumab | Praxbind) & (dabigatran | Pradaxa) & reversal & (\"GI bleeding\" | hemorrhage) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMeta-learning for few-shot capsule endoscopy <|end|>\n<|assistant|> \n(meta-learning | few-shot) & (capsule | VCE) & endoscopy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in deep brain stimulation surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (DBS | \"deep brain stimulation\") & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and metabolic reprogramming in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (metabolic | reprogramming) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDistributed RL for cuff pressure <|end|>\n<|assistant|> \n(distributed | reinforcement) & learning & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome engraftment kinetics <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | engraftment) & kinetics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-bariatric leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post bariatric\" | surgery) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nWells score in cancer patients for PE <|end|>\n<|assistant|> \n(Wells | score) & cancer & (PE | \"pulmonary embolism\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in congenital heart disease with shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & (\"congenital heart disease\" | CHD) & shock <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotypes in cardiac surgery <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in pediatric pneumonia <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & pediatric & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute myocarditis recovery <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & acute & myocarditis & recovery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag for HELLP thrombocytopenia <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & thrombocytopenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSecure enclaves for INR processing <|end|>\n<|assistant|> \n(secure | enclaves) & (INR | \"international ratio\") & processing <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume percent predicted in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | percent) & predicted & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer local recurrence <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"local recurrence\" | recurrence) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC treatment response <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (treatment | response) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in primary care <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & (\"primary care\" | community) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for bacterial coinfection in COVID <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (bacterial | coinfection) & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in ART pregnancies <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (ART | \"assisted reproductive technology\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in intensive care <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"intensive care\" | ICU) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis education <|end|>\n<|assistant|> \n(3D | printed) & simulators & pericardiocentesis & education <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for thumb base OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & (\"thumb base\" | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after ERCP for gallstones <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & gallstones <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in COVID macrophage activation <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (COVID | SARS-CoV-2) & (\"macrophage activation\" | MAS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThigh BP measurement in obesity <|end|>\n<|assistant|> \n(thigh | BP) & measurement & obesity <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebral-placental ratio in early-onset IUGR <|end|>\n<|assistant|> \n(cerebral-placental | CPR) & ratio & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in HIV on ART <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (HIV | \"human immunodeficiency virus\") & (ART | \"antiretroviral therapy\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and delirium in ICU <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & delirium & (ICU | \"intensive care\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin vs terlipressin/albumin for HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (terlipressin/albumin | combination) & (HRS | \"hepatorenal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech PFO occluder long-term <|end|>\n<|assistant|> \n(Occlutech | occluder) & (PFO | \"patent foramen ovale\") & \"long term\" <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAccelerate Pheno for Gram-negative bacteremia <|end|>\n<|assistant|> \n(Accelerate | Pheno) & (\"Gram-negative\" | bacteremia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in chemotherapy-related cardiac dysfunction <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (chemotherapy | induced) & (\"cardiac dysfunction\" | cardiotoxicity) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time vs ScvO2 <|end|>\n<|assistant|> \n(lactate | clearance) & time & (ScvO2 | \"central venous oxygen saturation\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS return-to-sport <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (\"return to sport\" | athletics) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and edema in ICH <|end|>\n<|assistant|> \n(systolic | BP) & edema & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte trajectories <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & trajectories <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD for Graves' disease radioiodine <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & (\"Graves'' disease\" | hyperthyroidism) & (radioiodine | I-131) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS NMIBC <|end|>\n<|assistant|> \n(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\") & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after complex PCI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (complex | high-risk) & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma Xpression for thyroid nodules <|end|>\n<|assistant|> \n(Afirma | Xpression) & (\"thyroid nodules\" | nodules) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TAP block efficacy <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TAP | \"transverse abdominis plane\") & block & efficacy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAtorvastatin for CIN prevention meta-analysis <|end|>\n<|assistant|> \n(atorvastatin | Lipitor) & prevention & (CIN | \"contrast-induced nephropathy\") & meta-analysis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAndexanet for factor Xa inhibitor reversal in GIB <|end|>\n<|assistant|> \n(andexanet | Andexxa) & (\"factor Xa\" | inhibitor) & reversal & (GIB | \"gastrointestinal bleeding\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy video classification <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy & video & classification <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in epilepsy monitoring <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (epilepsy | seizures) & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and mitochondrial dynamics in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (mitochondrial | dynamics) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHierarchical meta-RL for cuff pressure <|end|>\n<|assistant|> \n\"hierarchical meta reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome predictors of cure <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | predictors) & cure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-esophagectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post esophagectomy\" | esophagectomy) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for DVT history <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (DVT | \"deep vein thrombosis\") & history <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric cardiogenic shock <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (cardiogenic | shock) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotypes in sepsis <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in unilateral ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (unilateral | ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute decompensated HF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (ADHF | \"acute decompensated heart failure\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag in HELLP with severe thrombocytopenia <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & (severe | thrombocytopenia) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nZero-knowledge proofs for INR data sharing <|end|>\n<|assistant|> \n(zero-knowledge | proofs) & (INR | \"international ratio\") & (\"data sharing\" | privacy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung area to head circumference ratio in CDH <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung area to head circumference\" | LHR) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer distant recurrence <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"distant recurrence\" | metastasis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD treatment de-escalation <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"de escalation\" | reduction) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in anticoagulated patients <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & anticoagulated <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic stewardship in VAP <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (antibiotic | stewardship) & (VAP | \"ventilator associated pneumonia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in preterm PROM <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (PROM | \"premature rupture of membranes\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine for AWS in mechanically ventilated patients <|end|>\n<|assistant|> \nketamine & (AWS | \"alcohol withdrawal syndrome\") & (\"mechanically ventilated\" | MV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed patient-specific pericardiocentesis guides <|end|>\n<|assistant|> \n(3D | printed) & patient-specific & pericardiocentesis & guides <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for thumb CMC OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & (thumb | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after ERCP for acute cholangitis <|end|>\n<|assistant|> \nearly & cholecystectomy & (ERCP | \"endoscopic retrograde cholangiopancreatography\") & (\"acute cholangitis\" | cholangitis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in HLH secondary to malignancy <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (HLH | \"hemophagocytic lymphohistiocytosis\") & (secondary | malignancy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nUpper arm circumference-based cuff size guidelines <|end|>\n<|assistant|> \n(upper arm | circumference) & based & cuff & size & guidelines <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMiddle cerebral artery Doppler in IUGR management <|end|>\n<|assistant|> \n(MCA | \"middle cerebral artery\") & Doppler & (IUGR | \"intrauterine growth restriction\") & management <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in renal transplant recipients <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"renal transplant\" | transplantation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ARDS in COVID <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (ARDS | \"acute respiratory distress syndrome\") & (COVID | SARS-CoV-2) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for type 2 HRS <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (\"type 2\" | HRS) & (\"hepatorenal syndrome\" | HRS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAmplatzer PFO occluder residual shunt <|end|>\n<|assistant|> \n(Amplatzer | occluder) & (PFO | \"patent foramen ovale\") & (\"residual shunt\" | shunt) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nT2Candida for candidemia in ICU <|end|>\n<|assistant|> \n(T2Candida | panel) & candidemia & (ICU | \"intensive care\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiac amyloidosis monitoring <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (\"cardiac amyloidosis\" | AL) & monitoring <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time for sepsis mortality <|end|>\n<|assistant|> \n(lactate | clearance) & time & sepsis & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS surgical outcomes <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (surgical | outcomes) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and midline shift in ICH <|end|>\n<|assistant|> \n(systolic | BP) & (\"midline shift\" | MLS) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte maturation <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & maturation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRadioiodine dose calculation for toxic nodular goiter <|end|>\n<|assistant|> \n(radioiodine | I-131) & dose & calculation & (\"toxic nodular goiter\" | MNG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVicinium for high-grade Ta NMIBC <|end|>\n<|assistant|> \n(vicinium | oportuzumab) & (\"high grade\" | Ta) & (NMIBC | \"non-muscle invasive bladder cancer\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after CABG <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (CABG | \"coronary artery bypass\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAfirma GSC for follicular thyroid lesions <|end|>\n<|assistant|> \n(Afirma | GSC) & (follicular | lesions) & thyroid <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap donor site pain <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & (\"donor site\" | abdomen) & pain <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in PCI <|end|>\n<|assistant|> \n(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for apixaban reversal in ICH <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (apixaban | Eliquis) & reversal & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy frame classification <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy & frame & classification <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in brain aneurysm surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"brain aneurysm\" | aneurysm) & surgery <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and immunometabolism in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (immunometabolism | metabolism) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFederated RL for cuff pressure optimization <|end|>\n<|assistant|> \n\"federated reinforcement learning\" & cuff & pressure & optimization <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome functional profiles <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | functional) & profiles <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-pancreatectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post pancreatectomy\" | pancreatectomy) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for autoimmune disease <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (autoimmune | disease) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric pulmonary hypertension <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"pulmonary hypertension\" | PAH) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI endotypes in cardiac surgery <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & endotypes & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with reduced EF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFrEF | \"heart failure with reduced ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLusutrombopag dosing in pregnancy <|end|>\n<|assistant|> \n(lusutrombopag | Mulpleta) & dosing & pregnancy <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHomomorphic encryption for INR analytics <|end|>\n<|assistant|> \n(homomorphic | encryption) & (INR | \"international ratio\") & analytics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume to fetal body volume ratio <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume to fetal body volume\" | ratio) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer disease-free survival <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"disease free survival\" | DFS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC deep remission <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (\"deep remission\" | remission) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in stroke patients <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & stroke <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for antibiotic duration in sepsis <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & antibiotics & duration & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in women with prior PTB <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (prior | history) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine vs propofol for AWS <|end|>\n<|assistant|> \nketamine & (propofol | Diprivan) & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed phantoms for pericardiocentesis simulation <|end|>\n<|assistant|> \n(3D | printed) & phantoms & pericardiocentesis & simulation <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous protein solution for erosive thumb OA <|end|>\n<|assistant|> \n(autologous | protein) & solution & erosive & (thumb | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSame-admission cholecystectomy after endoscopic stone extraction <|end|>\n<|assistant|> \n(same-admission | immediate) & cholecystectomy & endoscopic & (\"stone extraction\" | removal) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in EBV-associated HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (EBV | \"Epstein Barr virus\") & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThigh BP in clinical practice <|end|>\n<|assistant|> \n(thigh | BP) & measurement & (\"clinical practice\" | accuracy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDuctus venosus Doppler in early-onset IUGR <|end|>\n<|assistant|> \n(ductus venosus | DV) & Doppler & (\"early onset\" | early) & (IUGR | \"intrauterine growth restriction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in solid organ transplant <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (\"solid organ transplant\" | transplantation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and ventilator-associated pneumonia <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (VAP | \"ventilator associated pneumonia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute kidney injury in cirrhosis <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & (AKI | \"acute kidney injury\") & cirrhosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nGore Cardioform ASD device <|end|>\n<|assistant|> \n(Gore | Cardioform) & device & (ASD | \"atrial septal defect\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBioFire BCID2 for pediatric ICU sepsis <|end|>\n<|assistant|> \n(BioFire | BCID2) & pediatric & (ICU | \"intensive care\") & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in cardiotoxicity monitoring <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & (cardiotoxicity | monitoring) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate/albumin ratio for septic shock mortality <|end|>\n<|assistant|> \n(lactate/albumin | ratio) & (\"septic shock\" | sepsis) & mortality <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS economic evaluation <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & (economic | evaluation) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP and perihematomal edema <|end|>\n<|assistant|> \n(systolic | BP) & (\"perihematomal edema\" | edema) & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte spatial organization <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & (spatial | organization) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nActivity-based dosing for Graves' radioiodine <|end|>\n<|assistant|> \n(activity | MIRD) & based & dosing & (\"Graves'' disease\" | hyperthyroidism) & (radioiodine | I-131) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nInstiladrin for CIS bladder cancer <|end|>\n<|assistant|> \n(Instiladrin | nadofaragene) & (CIS | \"carcinoma in situ\") & (\"bladder cancer\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after left main stenting <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (\"left main\" | LM) & stenting <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosettaGX Reveal for indeterminate FNA <|end|>\n<|assistant|> \n(RosettaGX | Reveal) & (indeterminate | FNA | \"fine needle aspiration\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in DIEP flap analgesia <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (DIEP | \"breast reconstruction\") & analgesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHigh-dose atorvastatin for CIN in CKD <|end|>\n<|assistant|> \n(high dose | statin) & atorvastatin & (CIN | \"contrast-induced nephropathy\") & (CKD | \"chronic kidney disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nIdarucizumab for ICH on dabigatran <|end|>\n<|assistant|> \n(idarucizumab | Praxbind) & (ICH | \"intracerebral hemorrhage\") & (dabigatran | Pradaxa) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFew-shot learning for capsule endoscopy lesion detection <|end|>\n<|assistant|> \n\"few shot learning\" & (capsule | VCE) & endoscopy & lesion & detection <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in awake craniotomy <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & (\"awake craniotomy\" | craniotomy) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and epigenetic changes in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (epigenetic | changes) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDistributed meta-RL for cuff pressure <|end|>\n<|assistant|> \n\"distributed meta reinforcement learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome metabolic pathways <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | metabolic) & pathways <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-gastrectomy leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post gastrectomy\" | gastrectomy) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for liver disease <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (\"liver disease\" | cirrhosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric septic shock with MODS <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"septic shock\" | sepsis) & (MODS | \"multiple organ dysfunction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI subphenotypes in burn sepsis <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & subphenotypes & (burn | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in trauma ARDS <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (trauma | injury) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with preserved EF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFpEF | \"heart failure with preserved ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag vs standard care in HELLP <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (\"standard care\" | conventional) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSecure multi-party computation for INR analytics <|end|>\n<|assistant|> \n(secure | multi-party) & computation & (INR | \"international ratio\") & analytics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI lung volume Z-scores in congenital diaphragmatic hernia <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"lung volume\" | Z-scores) & (CDH | \"congenital diaphragmatic hernia\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer overall survival <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (\"overall survival\" | OS) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for CD mucosal healing <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (CD | \"Crohn''s disease\") & (\"mucosal healing\" | healing) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCYP2C19 testing in elderly with ACS <|end|>\n<|assistant|> \n(CYP2C19 | genotype) & testing & elderly & (ACS | \"acute coronary syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nProcalcitonin kinetics for bacterial sepsis <|end|>\n<|assistant|> \n(PCT | procalcitonin) & kinetics & (bacterial | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMulti-omics PTB prediction in women with short cervix <|end|>\n<|assistant|> \n(multi-omics | integrated) & (PTB | \"preterm birth\") & prediction & (\"short cervix\" | cervical) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nKetamine-propofol combination for severe AWS <|end|>\n<|assistant|> \n(ketamine | propofol) & combination & severe & (AWS | \"alcohol withdrawal syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n3D printed simulators for pericardiocentesis training <|end|>\n<|assistant|> \n(3D | printed) & simulators & pericardiocentesis & training <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAutologous micro-fragmented adipose tissue for erosive thumb OA <|end|>\n<|assistant|> \n(autologous | micro-fragmented) & (adipose | fat) & tissue & erosive & (thumb | CMC) & osteoarthritis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEarly cholecystectomy after endoscopic sphincterotomy for choledocholithiasis <|end|>\n<|assistant|> \nearly & cholecystectomy & (\"endoscopic sphincterotomy\" | ERCP) & choledocholithiasis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nHScore in malignancy-associated HLH <|end|>\n<|assistant|> \n(HScore | \"HScore\") & (malignancy | associated) & (HLH | \"hemophagocytic lymphohistiocytosis\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThigh BP measurement guidelines <|end|>\n<|assistant|> \n(thigh | BP) & measurement & guidelines <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCerebroplacental ratio in IUGR prognosis <|end|>\n<|assistant|> \n(cerebroplacental | CPR) & ratio & (IUGR | \"intrauterine growth restriction\") & prognosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nPCSK9 inhibitors in inflammatory bowel disease <|end|>\n<|assistant|> \n(PCSK9 | inhibitor) & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVitamin D and COVID-19 ARDS <|end|>\n<|assistant|> \n(vitamin D | cholecalciferol) & (COVID | SARS-CoV-2) & (ARDS | \"acute respiratory distress syndrome\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTerlipressin for acute variceal hemorrhage <|end|>\n<|assistant|> \n(terlipressin | vasopressin) & acute & (\"variceal hemorrhage\" | bleeding) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nOcclutech Figulla Flex II PFO <|end|>\n<|assistant|> \n(Occlutech | Figulla) & device & (PFO | \"patent foramen ovale\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSeptiFast for sepsis in neutropenia <|end|>\n<|assistant|> \n(SeptiFast | PCR) & sepsis & neutropenia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nECV in monitoring cardiac sarcoid <|end|>\n<|assistant|> \n(ECV | \"extracellular volume\") & monitoring & (\"cardiac sarcoid\" | sarcoidosis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLactate clearance time prognostic value in septic shock <|end|>\n<|assistant|> \n(lactate | clearance) & time & (prognostic | value) & (\"septic shock\" | sepsis) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystem dynamics for CTS prevention cost-effectiveness <|end|>\n<|assistant|> \n\"system dynamics\" & (CTS | \"carpal tunnel syndrome\") & prevention & (cost-effectiveness | economic) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSystolic BP reduction and ICH expansion <|end|>\n<|assistant|> \n(systolic | BP) & reduction & (ICH | \"intracerebral hemorrhage\") & expansion <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSelf-supervised learning for lymphocyte transcriptomics <|end|>\n<|assistant|> \n\"self supervised learning\" & lymphocyte & transcriptomics <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nMIRD vs standard dosing for toxic adenoma <|end|>\n<|assistant|> \n(MIRD | \"medical internal radiation dose\") & (standard | dosing) & (\"toxic adenoma\" | nodule) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCG0070 for BCG-unresponsive bladder cancer <|end|>\n<|assistant|> \n(CG0070 | oncolytic) & (BCG-unresponsive | resistant) & (\"bladder cancer\" | NMIBC) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nTicagrelor monotherapy after ACS with PCI <|end|>\n<|assistant|> \n(ticagrelor | Brilinta) & monotherapy & (ACS | \"acute coronary syndrome\") & (PCI | \"percutaneous coronary intervention\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nThyraMIR for thyroid cancer diagnosis <|end|>\n<|assistant|> \n(ThyraMIR | miRNA) & (\"thyroid cancer\" | malignancy) & diagnosis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nLiposomal bupivacaine in TRAM flap donor site analgesia <|end|>\n<|assistant|> \n(liposomal | Exparel) & bupivacaine & (TRAM | \"breast reconstruction\") & (\"donor site\" | abdomen) & analgesia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRosuvastatin for CIN prevention in renal impairment <|end|>\n<|assistant|> \n(rosuvastatin | Crestor) & prevention & (CIN | \"contrast-induced nephropathy\") & (\"renal impairment\" | CKD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCiraparantag for edoxaban reversal in ICH <|end|>\n<|assistant|> \n(ciraparantag | PER977) & (edoxaban | Savaysa) & reversal & (ICH | \"intracerebral hemorrhage\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nVision transformers for capsule endoscopy video summarization <|end|>\n<|assistant|> \n(ViT | \"vision transformers\") & (capsule | VCE) & endoscopy & video & summarization <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nBIS in pediatric brain surgery <|end|>\n<|assistant|> \n(BIS | \"bispectral index\") & pediatric & (\"brain surgery\" | neurosurgery) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nCorticosteroids and gut microbiome in sepsis <|end|>\n<|assistant|> \n(corticosteroids | steroids) & (\"gut microbiome\" | microbiota) & sepsis <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFederated meta-learning for cuff pressure <|end|>\n<|assistant|> \n\"federated meta learning\" & cuff & pressure <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFMT microbiome virome <|end|>\n<|assistant|> \n(FMT | \"fecal microbiota transplantation\") & (microbiome | virome) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEndoscopic vacuum for post-Whipple leaks <|end|>\n<|assistant|> \nendoscopic & vacuum & (\"post Whipple\" | pancreaticoduodenectomy) & leaks <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nD-dimer adjusted for inflammatory bowel disease <|end|>\n<|assistant|> \n(D-dimer | \"fibrin fragment\") & adjusted & (IBD | \"inflammatory bowel disease\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAngiotensin II in pediatric congenital heart surgery <|end|>\n<|assistant|> \n(angiotensin II | Giapreza) & pediatric & (\"congenital heart surgery\" | CHD) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\n[TIMP-2]•[IGFBP7] for AKI phenotyping in cardiac surgery <|end|>\n<|assistant|> \n([TIMP-2]•[IGFBP7] | NephroCheck) & (AKI | \"acute kidney injury\") & phenotyping & (\"cardiac surgery\" | CABG) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nEIT for PEEP titration in COVID pneumonia <|end|>\n<|assistant|> \n(EIT | \"electrical impedance tomography\") & (PEEP | \"positive end-expiratory pressure\") & titration & (COVID | SARS-CoV-2) & pneumonia <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nSacubitril/valsartan in acute HF with mid-range EF <|end|>\n<|assistant|> \n(sacubitril/valsartan | Entresto) & (\"acute HF\" | AHF) & (HFmrEF | \"heart failure with mid-range ejection fraction\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nAvatrombopag for HELLP syndrome outcomes <|end|>\n<|assistant|> \n(avatrombopag | Doptelet) & (HELLP | \"hemolysis elevated liver enzymes low platelets\") & outcomes <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nDifferential privacy for INR datasets <|end|>\n<|assistant|> \n(differential | privacy) & (INR | \"international ratio\") & datasets <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFetal MRI observed/expected total fetal lung volume <|end|>\n<|assistant|> \n(fetal | prenatal) & (MRI | \"magnetic resonance\") & (\"observed/expected total fetal lung volume\" | O/E TFLV) <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nRobotic rectal cancer quality-adjusted life years <|end|>\n<|assistant|> \nrobotic & (\"rectal cancer\" | rectum) & surgery & (QALY | \"quality adjusted life years\") <|end|>"}
{"text": "<|user|>\nConvert this medical query to a PostgreSQL tsquery expression:\nFecal calprotectin for UC endoscopic improvement <|end|>\n<|assistant|> \n(fecal | stool) & calprotectin & (UC | \"ulcerative colitis\") & (endoscopic | improvement) <|end|>"}
