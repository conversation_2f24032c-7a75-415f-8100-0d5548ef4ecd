#!/usr/bin/env python3
"""
Test script to verify the file selector functionality works.
"""

from pathlib import Path
from ui.qas_reviewer import QASEntry

def test_sample_file_loading():
    """Test loading the sample Q/A/S file."""
    print("Testing Q/A/S File Loading")
    print("=" * 40)
    
    # Test with sample file
    sample_file = Path("examples/sample_qas.txt")
    
    if not sample_file.exists():
        print(f"❌ Sample file not found: {sample_file}")
        return False
    
    print(f"✅ Sample file found: {sample_file}")
    
    # Test file parsing without UI
    from ui.qas_reviewer import QASReviewerApp
    
    # Create app without UI setup
    app = QASReviewerApp.__new__(QASReviewerApp)
    app.db_helper = None  # Skip database for this test
    
    try:
        entries = app.parse_qas_file(sample_file)
        print(f"✅ Parsed {len(entries)} entries from sample file")
        
        if len(entries) > 0:
            print(f"   First entry: Q='{entries[0].question[:50]}...'")
            print(f"   First source: S='{entries[0].source}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error parsing file: {e}")
        return False

def test_path_handling():
    """Test different path formats."""
    print("\nTesting Path Handling")
    print("=" * 40)
    
    test_paths = [
        "examples/sample_qas.txt",
        "./examples/sample_qas.txt", 
        "file://examples/sample_qas.txt",
        str(Path("examples/sample_qas.txt").absolute())
    ]
    
    for path_str in test_paths:
        try:
            # Simulate the path processing from load_file_from_input
            if path_str.startswith('file://'):
                path_str = path_str[7:]
            
            file_path = Path(path_str).expanduser().resolve()
            
            if file_path.exists():
                print(f"✅ Path resolved: '{path_str}' -> {file_path}")
            else:
                print(f"⚠️  Path resolved but file not found: '{path_str}' -> {file_path}")
                
        except Exception as e:
            print(f"❌ Error with path '{path_str}': {e}")

def main():
    """Run file selector tests."""
    print("Q/A/S File Selector Tests")
    print("=" * 50)
    
    success = test_sample_file_loading()
    test_path_handling()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ File selector functionality working!")
        print("\nTo test the full UI:")
        print("1. Run: python app/qas_reviewer_app.py")
        print("2. Enter: examples/sample_qas.txt")
        print("3. Click 'Load File' or press Enter")
    else:
        print("❌ File selector tests failed")

if __name__ == "__main__":
    main()
