#!/usr/bin/env python3
"""
Q/A/S Human Reviewer Application

Standalone application for human reviewers to edit Q/A/S formatted text files.
Run this script to launch the flet-based user interface.

Usage:
    python app/qas_reviewer_app.py

Requirements:
    - flet library for UI
    - PostgreSQL database with document table
    - Environment variables for database connection
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import flet as ft
from ui.qas_reviewer import QASReviewerApp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('qas_reviewer.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def main(page: ft.Page):
    """Main entry point for the Q/A/S reviewer application."""
    try:
        # Set up the page
        page.title = "Q/A/S Human Reviewer"
        page.window.width = 1200
        page.window.height = 800
        page.window.resizable = True

        # Create and run the application
        QASReviewerApp(page)
        logger.info("Q/A/S Reviewer application started successfully")
        
    except Exception as e:
        logger.error(f"Error starting application: {e}")
        # Show error to user
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Text("Error Starting Application", size=24, color="red"),
                    ft.Text(f"Error: {str(e)}", size=14),
                    ft.Text("Please check the logs for more details.", size=12),
                    ft.ElevatedButton("Close", on_click=lambda _: page.window.close())
                ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=20,
                alignment=ft.alignment.center
            )
        )


def check_environment():
    """Check if required environment variables are set."""
    required_vars = ['POSTGRES_HOST', 'POSTGRES_DB', 'POSTGRES_USER', 'POSTGRES_PASSWORD']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("Error: Missing required environment variables:")
        for var in missing_vars:
            print(f"  - {var}")
        print("\nPlease set these environment variables before running the application.")
        print("You can use a .env file in the project root directory.")
        return False
    
    return True


if __name__ == "__main__":
    print("Q/A/S Human Reviewer Application")
    print("=" * 40)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    print("Starting application...")
    
    try:
        # Run the flet application
        ft.app(target=main)
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"Fatal error: {e}")
        sys.exit(1)
