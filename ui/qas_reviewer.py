#!/usr/bin/env python3
"""
Q/A/S Human Reviewer UI

A flet-based user interface for human reviewers to edit Q/A/S formatted text files.
Features file selection, editable Q/A fields, source display, and markdown abstract viewer.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple, cast
import flet as ft
from helpers.database import DatabaseHelper

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QASEntry:
    """Represents a single Q/A/S entry."""
    
    def __init__(self, question: str = "", answer: str = "", source: str = ""):
        self.question = question
        self.answer = answer
        self.source = source
        self.original_question = question
        self.original_answer = answer
        self.is_modified = False

    def mark_modified(self):
        """Mark this entry as modified."""
        self.is_modified = True

    def is_changed(self) -> bool:
        """Check if this entry has been changed from original."""
        return (self.question != self.original_question or 
                self.answer != self.original_answer)


class QASReviewerApp:
    """Main application class for the Q/A/S reviewer."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "Q/A/S Human Reviewer"
        self.page.window_width = 1200
        self.page.window_height = 800
        
        # Application state
        self.current_file_path: Optional[Path] = None
        self.qas_entries: List[QASEntry] = []
        self.current_entry_index = 0
        self.db_helper = DatabaseHelper()
        self.has_unsaved_changes = False
        
        # UI components - will be initialized in setup_ui
        self.file_picker = None
        self.question_field = None
        self.answer_field = None
        self.source_field = None
        self.abstract_display = None
        self.status_text = None
        self.entry_counter = None
        self.prev_button = None
        self.next_button = None
        self.save_button = None
        
        self.setup_ui()

    def setup_ui(self):
        """Set up the user interface."""
        # File picker
        self.file_picker = ft.FilePicker(
            on_result=self.on_file_selected
        )
        self.page.overlay.append(self.file_picker)
        
        # Header with file selection
        header = ft.Container(
            content=ft.Row([
                ft.Text("Q/A/S Human Reviewer", size=24, weight=ft.FontWeight.BOLD),
                ft.ElevatedButton(
                    "Select Q/A/S File",
                    icon=ft.Icons.FOLDER_OPEN,
                    on_click=lambda _: self.file_picker.pick_files(
                        dialog_title="Select Q/A/S text file",
                        file_type=ft.FilePickerFileType.CUSTOM,
                        allowed_extensions=["txt"]
                    )
                )
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=20,
            bgcolor="bluegrey50",
            border_radius=10
        )
        
        # Status and navigation
        self.status_text = ft.Text("No file loaded", color="grey600")
        self.entry_counter = ft.Text("", weight=ft.FontWeight.BOLD)
        
        self.prev_button = ft.IconButton(
            icon=ft.Icons.ARROW_BACK,
            tooltip="Previous entry",
            on_click=self.go_to_previous,
            disabled=True
        )

        self.next_button = ft.IconButton(
            icon=ft.Icons.ARROW_FORWARD,
            tooltip="Next entry",
            on_click=self.go_to_next,
            disabled=True
        )
        
        navigation = ft.Row([
            self.status_text,
            ft.Container(expand=True),
            self.prev_button,
            self.entry_counter,
            self.next_button
        ])
        
        # Q/A/S editing fields
        self.question_field = ft.TextField(
            label="Question",
            multiline=True,
            min_lines=2,
            max_lines=4,
            on_change=self.on_question_changed,
            disabled=True
        )
        
        self.answer_field = ft.TextField(
            label="Answer",
            multiline=True,
            min_lines=3,
            max_lines=6,
            on_change=self.on_answer_changed,
            disabled=True
        )
        
        self.source_field = ft.TextField(
            label="Source (DOI or PMID)",
            read_only=True,
            disabled=True
        )
        
        # Save button
        self.save_button = ft.ElevatedButton(
            "Save Changes",
            icon=ft.Icons.SAVE,
            on_click=self.save_changes,
            disabled=True
        )
        
        # Abstract display
        self.abstract_display = ft.Markdown(
            value="*Select a file and entry to view abstract*"
        )
        
        abstract_container = ft.Container(
            content=ft.Column([
                ft.Text("Publication Abstract", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(
                    content=self.abstract_display,
                    border=ft.border.all(1, "grey300"),
                    border_radius=5,
                    padding=10,
                    height=300,
                    bgcolor="grey50"
                )
            ]),
            expand=True
        )
        
        # Main layout
        left_panel = ft.Container(
            content=ft.Column([
                self.question_field,
                self.answer_field,
                self.source_field,
                self.save_button
            ], spacing=10),
            width=500,
            padding=10
        )
        
        main_content = ft.Row([
            left_panel,
            ft.VerticalDivider(width=1),
            abstract_container
        ], expand=True)
        
        # Complete layout
        self.page.add(
            ft.Column([
                header,
                ft.Divider(height=1),
                navigation,
                ft.Divider(height=1),
                main_content
            ], expand=True)
        )

    def on_file_selected(self, e: ft.FilePickerResultEvent):
        """Handle file selection."""
        if e.files:
            file_path = Path(e.files[0].path)
            self.load_qas_file(file_path)

    def load_qas_file(self, file_path: Path):
        """Load Q/A/S entries from a text file."""
        try:
            # Validate file exists and is readable
            if not file_path.exists():
                self.show_error(f"File does not exist: {file_path}")
                return

            if not file_path.is_file():
                self.show_error(f"Path is not a file: {file_path}")
                return

            # Check file size (warn if very large)
            file_size = file_path.stat().st_size
            if file_size > 10 * 1024 * 1024:  # 10MB
                self.show_error("File is very large (>10MB). This may cause performance issues.")
                return

            self.current_file_path = file_path
            self.qas_entries = self.parse_qas_file(file_path)

            if self.qas_entries:
                self.current_entry_index = 0
                self.enable_ui()
                self.display_current_entry()
                self.status_text.value = f"Loaded: {file_path.name} ({len(self.qas_entries)} entries)"

                # Connect to database
                if self.db_helper.connect():
                    logger.info("Database connected successfully")
                else:
                    self.show_error("Warning: Could not connect to database. Abstract display will be unavailable.")
            else:
                self.show_error("No valid Q/A/S entries found in the file. Expected format: Q: question\\nA: answer\\nS: source")

        except PermissionError:
            self.show_error(f"Permission denied reading file: {file_path}")
        except UnicodeDecodeError:
            self.show_error(f"File encoding error. Please ensure the file is UTF-8 encoded: {file_path}")
        except Exception as e:
            self.show_error(f"Error loading file: {str(e)}")
            logger.error(f"Error loading file {file_path}: {e}")

        self.page.update()

    def parse_qas_file(self, file_path: Path) -> List[QASEntry]:
        """Parse Q/A/S format text file."""
        entries = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split by double newlines to separate entries
        blocks = content.split('\n\n')
        
        for block in blocks:
            lines = [line.strip() for line in block.strip().split('\n') if line.strip()]
            
            if len(lines) >= 3:
                question = ""
                answer = ""
                source = ""
                
                for line in lines:
                    if line.startswith("Q: "):
                        question = line[3:].strip()
                    elif line.startswith("A: "):
                        answer = line[3:].strip()
                    elif line.startswith("S: "):
                        source = line[3:].strip()
                
                if question and answer and source:
                    entries.append(QASEntry(question, answer, source))
        
        return entries

    def enable_ui(self):
        """Enable UI components when a file is loaded."""
        self.question_field.disabled = False
        self.answer_field.disabled = False
        self.source_field.disabled = False
        self.save_button.disabled = False
        self.prev_button.disabled = False
        self.next_button.disabled = False

    def display_current_entry(self):
        """Display the current Q/A/S entry."""
        if not self.qas_entries or self.current_entry_index >= len(self.qas_entries):
            return

        # Validate UI components are initialized
        if (self.question_field is None or self.answer_field is None or
            self.source_field is None or self.entry_counter is None or
            self.prev_button is None or self.next_button is None):
            logger.error("UI components not properly initialized")
            return

        entry = self.qas_entries[self.current_entry_index]

        self.question_field.value = entry.question
        self.answer_field.value = entry.answer
        self.source_field.value = entry.source

        # Update navigation
        self.entry_counter.value = f"{self.current_entry_index + 1} / {len(self.qas_entries)}"
        self.prev_button.disabled = self.current_entry_index == 0
        self.next_button.disabled = self.current_entry_index == len(self.qas_entries) - 1

        # Update save button state
        self.update_save_button_state()

        # Load and display abstract
        self.load_abstract_for_current_entry()

        self.page.update()

    def load_abstract_for_current_entry(self):
        """Load and display the abstract for the current entry."""
        if (not self.qas_entries or self.current_entry_index >= len(self.qas_entries) or
            self.abstract_display is None):
            return

        entry = self.qas_entries[self.current_entry_index]

        if self.db_helper.connection:
            doc_info = self.db_helper.get_abstract_by_source(entry.source)

            if doc_info:
                # Format as markdown
                markdown_content = f"""
# {doc_info['title']}

**Publication Date:** {doc_info['publication_date']}
**DOI:** {doc_info['doi'] or 'N/A'}
**PMID:** {doc_info['external_id'] or 'N/A'}

## Abstract

{doc_info['abstract']}
"""
                self.abstract_display.value = markdown_content
            else:
                self.abstract_display.value = f"*Abstract not found for source: {entry.source}*"
        else:
            self.abstract_display.value = "*Database not connected. Cannot load abstract.*"

    def on_question_changed(self, e):
        """Handle question field changes."""
        if self.qas_entries and self.current_entry_index < len(self.qas_entries):
            entry = self.qas_entries[self.current_entry_index]
            entry.question = e.control.value
            entry.mark_modified()
            self.has_unsaved_changes = True
            self.update_save_button_state()

    def on_answer_changed(self, e):
        """Handle answer field changes."""
        if self.qas_entries and self.current_entry_index < len(self.qas_entries):
            entry = self.qas_entries[self.current_entry_index]
            entry.answer = e.control.value
            entry.mark_modified()
            self.has_unsaved_changes = True
            self.update_save_button_state()

    def update_save_button_state(self):
        """Update the save button state based on changes."""
        if self.save_button is None:
            return

        has_changes = any(entry.is_changed() for entry in self.qas_entries)
        self.save_button.disabled = not has_changes

        if has_changes:
            self.save_button.text = "Save Changes *"
            self.save_button.bgcolor = "orange100"
        else:
            self.save_button.text = "Save Changes"
            self.save_button.bgcolor = None

    def go_to_previous(self, e):
        """Navigate to previous entry."""
        if self.current_entry_index > 0:
            self.current_entry_index -= 1
            self.display_current_entry()

    def go_to_next(self, e):
        """Navigate to next entry."""
        if self.current_entry_index < len(self.qas_entries) - 1:
            self.current_entry_index += 1
            self.display_current_entry()

    def save_changes(self, e):
        """Save changes to a new file with 'he_' prefix."""
        if not self.current_file_path or not self.qas_entries:
            return

        try:
            # Create new filename with 'he_' prefix
            original_name = self.current_file_path.name
            new_name = f"he_{original_name}"
            new_path = self.current_file_path.parent / new_name

            # Write the modified entries
            with open(new_path, 'w', encoding='utf-8') as f:
                for i, entry in enumerate(self.qas_entries):
                    f.write(f"Q: {entry.question}\n")
                    f.write(f"A: {entry.answer}\n")
                    f.write(f"S: {entry.source}\n")

                    # Add separator between entries (except for the last one)
                    if i < len(self.qas_entries) - 1:
                        f.write("\n")

            # Reset modification flags
            for entry in self.qas_entries:
                entry.original_question = entry.question
                entry.original_answer = entry.answer
                entry.is_modified = False

            self.has_unsaved_changes = False
            self.update_save_button_state()

            self.show_success(f"Changes saved to: {new_name}")
            logger.info(f"Saved changes to {new_path}")

        except Exception as e:
            self.show_error(f"Error saving file: {str(e)}")
            logger.error(f"Error saving file: {e}")

        self.page.update()

    def show_error(self, message: str):
        """Show error message to user."""
        logger.error(message)
        if self.status_text:
            self.status_text.value = f"Error: {message}"
            self.status_text.color = "red"
            self.page.update()

    def show_success(self, message: str):
        """Show success message to user."""
        logger.info(message)
        if self.status_text:
            self.status_text.value = message
            self.status_text.color = "green"
            self.page.update()


def main(page: ft.Page):
    """Main entry point for the application."""
    QASReviewerApp(page)


if __name__ == "__main__":
    ft.app(target=main)
