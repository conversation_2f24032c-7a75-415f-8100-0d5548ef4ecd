#!/usr/bin/env python3
"""
Unit tests for the database helper module.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import os
from helpers.database import DatabaseHelper


class TestDatabaseHelper(unittest.TestCase):
    """Test cases for DatabaseHelper class."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock environment variables
        self.env_vars = {
            'POSTGRES_HOST': 'localhost',
            'POSTGRES_PORT': '5432',
            'POSTGRES_DB': 'test_db',
            'POSTGRES_USER': 'test_user',
            'POSTGRES_PASSWORD': 'test_pass'
        }

    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_init_with_valid_env_vars(self):
        """Test initialization with valid environment variables."""
        helper = DatabaseHelper()
        self.assertEqual(helper.db_config['host'], 'localhost')
        self.assertEqual(helper.db_config['database'], 'test_db')
        self.assertEqual(helper.db_config['user'], 'test_user')
        self.assertEqual(helper.db_config['password'], 'test_pass')

    @patch.dict(os.environ, {}, clear=True)
    def test_init_missing_env_vars(self):
        """Test initialization with missing environment variables."""
        with self.assertRaises(ValueError) as context:
            DatabaseHelper()
        self.assertIn("Missing required environment variables", str(context.exception))

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_connect_success(self, mock_connect):
        """Test successful database connection."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock table existence check
        mock_cursor.fetchone.return_value = [True]
        
        helper = DatabaseHelper()
        result = helper.connect()
        
        self.assertTrue(result)
        self.assertEqual(helper.connection, mock_connection)
        self.assertEqual(helper.cursor, mock_cursor)
        mock_connect.assert_called_once()

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_connect_table_not_exists(self, mock_connect):
        """Test connection when document table doesn't exist."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock table existence check - table doesn't exist
        mock_cursor.fetchone.return_value = [False]
        
        helper = DatabaseHelper()
        result = helper.connect()
        
        self.assertFalse(result)

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_doi_found(self, mock_connect):
        """Test retrieving document by DOI when found."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock query result
        mock_result = ('Test Title', 'Test Abstract', '2025-01-01', '10.1234/test', '12345')
        mock_cursor.fetchone.return_value = mock_result
        
        helper = DatabaseHelper()
        helper.connection = mock_connection
        helper.cursor = mock_cursor
        
        result = helper.get_abstract_by_doi('10.1234/test')
        
        expected = {
            'title': 'Test Title',
            'abstract': 'Test Abstract',
            'publication_date': '2025-01-01',
            'doi': '10.1234/test',
            'external_id': '12345'
        }
        
        self.assertEqual(result, expected)
        mock_cursor.execute.assert_called_once()

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_doi_not_found(self, mock_connect):
        """Test retrieving document by DOI when not found."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock query result - not found
        mock_cursor.fetchone.return_value = None
        
        helper = DatabaseHelper()
        helper.connection = mock_connection
        helper.cursor = mock_cursor
        
        result = helper.get_abstract_by_doi('10.1234/notfound')
        
        self.assertIsNone(result)

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_pmid(self, mock_connect):
        """Test retrieving document by PMID."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock query result
        mock_result = ('Test Title', 'Test Abstract', '2025-01-01', '10.1234/test', '12345')
        mock_cursor.fetchone.return_value = mock_result
        
        helper = DatabaseHelper()
        helper.connection = mock_connection
        helper.cursor = mock_cursor
        
        result = helper.get_abstract_by_pmid('12345')
        
        expected = {
            'title': 'Test Title',
            'abstract': 'Test Abstract',
            'publication_date': '2025-01-01',
            'doi': '10.1234/test',
            'external_id': '12345'
        }
        
        self.assertEqual(result, expected)

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_source_doi(self, mock_connect):
        """Test retrieving document by source (DOI format)."""
        helper = DatabaseHelper()
        
        # Mock the get_abstract_by_doi method
        with patch.object(helper, 'get_abstract_by_doi') as mock_get_doi:
            mock_get_doi.return_value = {'title': 'Test'}
            
            result = helper.get_abstract_by_source('10.1234/test')
            
            mock_get_doi.assert_called_once_with('10.1234/test')
            self.assertEqual(result, {'title': 'Test'})

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_source_pmid(self, mock_connect):
        """Test retrieving document by source (PMID format)."""
        helper = DatabaseHelper()
        
        # Mock the get_abstract_by_pmid method
        with patch.object(helper, 'get_abstract_by_pmid') as mock_get_pmid:
            mock_get_pmid.return_value = {'title': 'Test'}
            
            result = helper.get_abstract_by_source('PMID:12345')
            
            mock_get_pmid.assert_called_once_with('12345')
            self.assertEqual(result, {'title': 'Test'})

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_get_abstract_by_source_unknown(self, mock_connect):
        """Test retrieving document by unknown source."""
        helper = DatabaseHelper()
        
        result = helper.get_abstract_by_source('Unknown')
        self.assertIsNone(result)
        
        result = helper.get_abstract_by_source('')
        self.assertIsNone(result)

    @patch('helpers.database.psycopg2.connect')
    @patch.dict(os.environ, {
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'test_db',
        'POSTGRES_USER': 'test_user',
        'POSTGRES_PASSWORD': 'test_pass'
    })
    def test_context_manager(self, mock_connect):
        """Test using DatabaseHelper as context manager."""
        # Mock connection and cursor
        mock_connection = Mock()
        mock_cursor = Mock()
        mock_connection.cursor.return_value = mock_cursor
        mock_connect.return_value = mock_connection
        
        # Mock table existence check
        mock_cursor.fetchone.return_value = [True]
        
        with DatabaseHelper() as helper:
            self.assertIsNotNone(helper.connection)
        
        # Verify disconnect was called
        mock_cursor.close.assert_called_once()
        mock_connection.close.assert_called_once()


if __name__ == '__main__':
    unittest.main()
