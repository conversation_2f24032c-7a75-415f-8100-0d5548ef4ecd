#!/usr/bin/env python3
"""
Unit tests for Q/A/S file parsing functionality.
"""

import unittest
import tempfile
from pathlib import Path
from ui.qas_reviewer import QASEntry, QASReviewerApp
from unittest.mock import Mock, patch
import flet as ft


class TestQASEntry(unittest.TestCase):
    """Test cases for QASEntry class."""

    def test_init(self):
        """Test QASEntry initialization."""
        entry = QASEntry("Test question?", "Test answer.", "10.1234/test")
        
        self.assertEqual(entry.question, "Test question?")
        self.assertEqual(entry.answer, "Test answer.")
        self.assertEqual(entry.source, "10.1234/test")
        self.assertEqual(entry.original_question, "Test question?")
        self.assertEqual(entry.original_answer, "Test answer.")
        self.assertFalse(entry.is_modified)

    def test_mark_modified(self):
        """Test marking entry as modified."""
        entry = QASEntry("Test question?", "Test answer.", "10.1234/test")
        self.assertFalse(entry.is_modified)
        
        entry.mark_modified()
        self.assertTrue(entry.is_modified)

    def test_is_changed(self):
        """Test detecting changes in entry."""
        entry = QASEntry("Test question?", "Test answer.", "10.1234/test")
        self.assertFalse(entry.is_changed())
        
        # Change question
        entry.question = "Modified question?"
        self.assertTrue(entry.is_changed())
        
        # Reset and change answer
        entry.question = "Test question?"
        entry.answer = "Modified answer."
        self.assertTrue(entry.is_changed())


class TestQASParser(unittest.TestCase):
    """Test cases for Q/A/S file parsing."""

    def setUp(self):
        """Set up test fixtures."""
        # Create a mock page for the app
        self.mock_page = Mock(spec=ft.Page)
        self.mock_page.overlay = []
        self.mock_page.window_width = 1200
        self.mock_page.window_height = 800
        self.mock_page.title = ""

    def create_test_file(self, content: str) -> Path:
        """Create a temporary test file with given content."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
        temp_file.write(content)
        temp_file.close()
        return Path(temp_file.name)

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_parse_valid_qas_file(self, mock_db_helper):
        """Test parsing a valid Q/A/S file."""
        content = """Q: What is the effect of aspirin on cardiovascular mortality?
A: Aspirin reduces cardiovascular mortality in high-risk patients.
S: 10.1234/aspirin-study

Q: How does MRI compare to CT for stroke detection?
A: MRI is more sensitive than CT for detecting early stroke signs.
S: PMID:12345678"""

        test_file = self.create_test_file(content)
        
        try:
            # Mock the database helper
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            entries = app.parse_qas_file(test_file)
            
            self.assertEqual(len(entries), 2)
            
            # Check first entry
            self.assertEqual(entries[0].question, "What is the effect of aspirin on cardiovascular mortality?")
            self.assertEqual(entries[0].answer, "Aspirin reduces cardiovascular mortality in high-risk patients.")
            self.assertEqual(entries[0].source, "10.1234/aspirin-study")
            
            # Check second entry
            self.assertEqual(entries[1].question, "How does MRI compare to CT for stroke detection?")
            self.assertEqual(entries[1].answer, "MRI is more sensitive than CT for detecting early stroke signs.")
            self.assertEqual(entries[1].source, "PMID:12345678")
            
        finally:
            test_file.unlink()  # Clean up

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_parse_file_with_extra_whitespace(self, mock_db_helper):
        """Test parsing file with extra whitespace and empty lines."""
        content = """

Q: What is the effect of aspirin?
A: Aspirin has cardiovascular benefits.
S: 10.1234/test


Q: How does exercise help?
A: Exercise improves cardiovascular health.
S: PMID:98765


"""

        test_file = self.create_test_file(content)
        
        try:
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            entries = app.parse_qas_file(test_file)
            
            self.assertEqual(len(entries), 2)
            self.assertEqual(entries[0].question, "What is the effect of aspirin?")
            self.assertEqual(entries[1].question, "How does exercise help?")
            
        finally:
            test_file.unlink()

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_parse_file_incomplete_entries(self, mock_db_helper):
        """Test parsing file with incomplete entries."""
        content = """Q: Complete question?
A: Complete answer.
S: 10.1234/complete

Q: Incomplete question?
A: Missing source.

Q: Another incomplete?
S: 10.1234/missing-answer

Q: What about this?
A: This has answer and source.
S: PMID:12345"""

        test_file = self.create_test_file(content)
        
        try:
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            entries = app.parse_qas_file(test_file)
            
            # Should only parse complete entries
            self.assertEqual(len(entries), 2)
            self.assertEqual(entries[0].source, "10.1234/complete")
            self.assertEqual(entries[1].source, "PMID:12345")
            
        finally:
            test_file.unlink()

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_parse_empty_file(self, mock_db_helper):
        """Test parsing an empty file."""
        content = ""
        test_file = self.create_test_file(content)
        
        try:
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            entries = app.parse_qas_file(test_file)
            
            self.assertEqual(len(entries), 0)
            
        finally:
            test_file.unlink()

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_parse_file_wrong_format(self, mock_db_helper):
        """Test parsing file with wrong format."""
        content = """This is not a Q/A/S format file.
It has some random text.
No proper structure."""

        test_file = self.create_test_file(content)
        
        try:
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            entries = app.parse_qas_file(test_file)
            
            self.assertEqual(len(entries), 0)
            
        finally:
            test_file.unlink()

    @patch('ui.qas_reviewer.DatabaseHelper')
    def test_save_changes_format(self, mock_db_helper):
        """Test saving changes maintains proper format."""
        # Create initial content
        content = """Q: Original question?
A: Original answer.
S: 10.1234/test"""

        test_file = self.create_test_file(content)
        
        try:
            mock_db_helper.return_value.connect.return_value = True
            
            app = QASReviewerApp(self.mock_page)
            app.current_file_path = test_file
            app.qas_entries = app.parse_qas_file(test_file)
            
            # Modify the entry
            app.qas_entries[0].question = "Modified question?"
            app.qas_entries[0].answer = "Modified answer."
            app.qas_entries[0].mark_modified()
            
            # Mock the save operation
            with patch('builtins.open', create=True) as mock_open:
                mock_file = Mock()
                mock_open.return_value.__enter__.return_value = mock_file
                
                app.save_changes(None)
                
                # Verify the write calls
                expected_calls = [
                    unittest.mock.call("Q: Modified question?\n"),
                    unittest.mock.call("A: Modified answer.\n"),
                    unittest.mock.call("S: 10.1234/test\n")
                ]
                
                mock_file.write.assert_has_calls(expected_calls)
            
        finally:
            test_file.unlink()


if __name__ == '__main__':
    unittest.main()
