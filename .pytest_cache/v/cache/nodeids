["tests/test_database_helper.py::TestDatabaseHelper::test_connect_success", "tests/test_database_helper.py::TestDatabaseHelper::test_connect_table_not_exists", "tests/test_database_helper.py::TestDatabaseHelper::test_context_manager", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_doi_found", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_doi_not_found", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_pmid", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_source_doi", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_source_pmid", "tests/test_database_helper.py::TestDatabaseHelper::test_get_abstract_by_source_unknown", "tests/test_database_helper.py::TestDatabaseHelper::test_init_missing_env_vars", "tests/test_database_helper.py::TestDatabaseHelper::test_init_with_valid_env_vars", "tests/test_qas_parser.py::TestQASEntry::test_init", "tests/test_qas_parser.py::TestQASEntry::test_is_changed", "tests/test_qas_parser.py::TestQASEntry::test_mark_modified", "tests/test_qas_parser.py::TestQASParser::test_parse_empty_file", "tests/test_qas_parser.py::TestQASParser::test_parse_file_incomplete_entries", "tests/test_qas_parser.py::TestQASParser::test_parse_file_with_extra_whitespace", "tests/test_qas_parser.py::TestQASParser::test_parse_file_wrong_format", "tests/test_qas_parser.py::TestQASParser::test_parse_valid_qas_file", "tests/test_qas_parser.py::TestQASParser::test_save_changes_format"]