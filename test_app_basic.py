#!/usr/bin/env python3
"""
Basic test script to verify the Q/A/S reviewer components work.
"""

import tempfile
from pathlib import Path
from ui.qas_reviewer import QASEntry
from helpers.database import DatabaseHelper
import os

def test_qas_entry():
    """Test QASEntry functionality."""
    print("Testing QASEntry...")
    
    entry = QASEntry("What is aspirin?", "A medication.", "10.1234/test")
    print(f"  Question: {entry.question}")
    print(f"  Answer: {entry.answer}")
    print(f"  Source: {entry.source}")
    print(f"  Is changed: {entry.is_changed()}")
    
    # Modify entry
    entry.question = "What is aspirin used for?"
    entry.mark_modified()
    print(f"  After modification - Is changed: {entry.is_changed()}")
    print("  ✓ QASEntry test passed")

def test_qas_parsing():
    """Test Q/A/S file parsing."""
    print("\nTesting Q/A/S file parsing...")
    
    # Create test content
    content = """Q: What is the effect of aspirin?
A: Aspirin reduces cardiovascular risk.
S: 10.1234/aspirin

Q: How does exercise help?
A: Exercise improves cardiovascular health.
S: PMID:12345"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(content)
        temp_path = Path(f.name)
    
    try:
        # Test parsing without UI
        from ui.qas_reviewer import QASReviewerApp
        
        # Create a mock page object
        class MockPage:
            def __init__(self):
                self.overlay = []
                self.window_width = 1200
                self.window_height = 800
                self.title = ""
            
            def add(self, *args):
                pass
            
            def update(self):
                pass
        
        # Create app without UI setup
        mock_page = MockPage()
        
        # Test just the parsing function
        app = QASReviewerApp.__new__(QASReviewerApp)  # Create without __init__
        app.db_helper = DatabaseHelper()
        
        entries = app.parse_qas_file(temp_path)
        
        print(f"  Parsed {len(entries)} entries")
        if len(entries) >= 2:
            print(f"  Entry 1: Q='{entries[0].question[:30]}...', S='{entries[0].source}'")
            print(f"  Entry 2: Q='{entries[1].question[:30]}...', S='{entries[1].source}'")
            print("  ✓ Q/A/S parsing test passed")
        else:
            print("  ✗ Q/A/S parsing test failed - wrong number of entries")
    
    finally:
        temp_path.unlink()  # Clean up

def test_database_helper():
    """Test database helper (without actual connection)."""
    print("\nTesting DatabaseHelper...")
    
    # Test environment variable validation
    try:
        # This should fail if env vars are not set
        helper = DatabaseHelper()
        print("  ✓ Environment variables are set")
        
        # Test source parsing logic
        test_cases = [
            ("10.1234/test", "DOI"),
            ("PMID:12345", "PMID"),
            ("Unknown", "Unknown"),
            ("", "Empty")
        ]
        
        for source, expected_type in test_cases:
            if source.startswith("PMID:"):
                print(f"  Source '{source}' -> PMID format")
            elif source and source != "Unknown":
                print(f"  Source '{source}' -> DOI format")
            else:
                print(f"  Source '{source}' -> {expected_type}")
        
        print("  ✓ DatabaseHelper test passed")
        
    except ValueError as e:
        print(f"  ⚠ DatabaseHelper test skipped: {e}")

def main():
    """Run basic tests."""
    print("Q/A/S Reviewer Basic Tests")
    print("=" * 40)
    
    test_qas_entry()
    test_qas_parsing()
    test_database_helper()
    
    print("\n" + "=" * 40)
    print("Basic tests completed!")
    print("\nTo run the full application:")
    print("  python app/qas_reviewer_app.py")

if __name__ == "__main__":
    main()
